#BUG GIVED UP NODE

# ///////////////



#BUG 已经弃用 无须维护



# ///////////////
#地主-农民 恶意掠夺 (短版本)
bbg_common.1_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.1.t
	desc = bbg_common.1.d_short
	trigger = {
		or = {
			has_law = law_type:law_serfdom
			has_law = law_type:law_tenant_farmers
		}
		NOT = {
			has_global_variable = bbg_common.1.over365
		}
	}
	duration = 3
	option = {
		name = bbg_common.1.a
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
	option = {
		name = bbg_common.1.b
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -1000
		}
	}
	option = {
		name = bbg_common.1.c
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 3
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
}

#地主-资本家 善意欺骗 (短版本)
bbg_common.2_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.2.t
	desc = bbg_common.2.d_short
	duration = 3
	immediate = {
		random_scope_state = {
			limit = {
				not = {
					is_capital = yes
				}
			}
			save_scope_as = state_random
		}
	}
	option = {
		name = bbg_common.2.a
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		scope:state_random = {
			create_building = {
				building = "building_university"
				level = 1
				reserves = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -4
			}
		}
	}
	option = {
		name = bbg_common.2.b
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
	}
	option = {
		name = bbg_common.2.c1
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 1000
		}
	}
	option = {
		name = bbg_common.2.c2
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_investment_pool = 1000
	}
}

#工人-实业家 工厂罢工 (短版本)
bbg_common.3_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.3.t
	desc = bbg_common.3.d_short
	duration = 3
	option = {
		name = bbg_common.3.a
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -5000
		}
	}
	option = {
		name = bbg_common.3.b
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
	}
	option = {
		name = bbg_common.3.c
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
}

#实业家-乡绅-人民 连锁店竞争 (短版本)
bbg_common.4_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.4.t
	desc = bbg_common.4.d_short
	duration = 3
	option = {
		name = bbg_common.4.a
		set_global_variable = {
			name = bbg_common.4.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 3000
		}
	}
	option = {
		name = bbg_common.4.b
		set_global_variable = {
			name = bbg_common.4.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 3
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
	option = {
		name = bbg_common.4.c
		set_global_variable = {
			name = bbg_common.4.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 1000
		}
	}
}

#宗教-知识分子 科学与宗教辩论 (短版本)
bbg_common.5_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.5.t
	desc = bbg_common.5.d_short
	duration = 3
	option = {
		name = bbg_common.5.a
		set_global_variable = {
			name = bbg_common.5.over365
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -1000
		}
	}
	option = {
		name = bbg_common.5.b
		set_global_variable = {
			name = bbg_common.5.over365
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
	option = {
		name = bbg_common.5.c
		set_global_variable = {
			name = bbg_common.5.over365
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 500
		}
	}
}

#实业家-农民 谷物法之争 (短版本)
bbg_common.6_short = {
	type = country_event
	placement = root
	title = bbg_common.6.t
	desc = bbg_common.6.d_short
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	duration = 3
	option = {
		name = bbg_common.6.a
		set_global_variable = {
			name = bbg_common.6.over365
			value = 0
			days = 365
		}
		add_treasury = -5000
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 365
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 365
			}
		}
	}
	option = {
		name = bbg_common.6.b
		set_global_variable = {
			name = bbg_common.6.over365
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 365
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 365
			}
		}
	}
	option = {
		name = bbg_common.6.c
		set_global_variable = {
			name = bbg_common.6.over365
			value = 0
			days = 365
		}
		add_treasury = -10000
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
	}
}

#工会-农民 团结的代价 (短版本)
bbg_common.7_short = {
	type = country_event
	placement = root
	title = bbg_common.7.t
	desc = bbg_common.7.d_short
	event_image = {
		video = "africa_public_protest"
	}
	duration = 3
	option = {
		name = bbg_common.7.a
		set_global_variable = {
			name = bbg_common.7.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 5
				days = 365
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 3
				days = 365
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -8000
		}
	}
	option = {
		name = bbg_common.7.b
		set_global_variable = {
			name = bbg_common.7.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -3
				days = 365
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -2
				days = 365
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 2000
		}
	}
	option = {
		name = bbg_common.7.c
		set_global_variable = {
			name = bbg_common.7.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -3000
		}
	}
}

#邻国 - 意外的边界冲突 (短版本)
bbg_common.8_short = {
	type = country_event
	placement = root
	title = bbg_common.8.t
	desc = bbg_common.8.d_short
	event_image = {
		video = "unspecific_airplane"
	}
	duration = 3
	immediate = {
		random_neighbouring_state = {
			limit = {
				has_building = building_barracks
			}
			owner = {
				save_scope_as = neighbor_country
			}
		}
	}
	option = {
		name = bbg_common.8.a
		set_global_variable = {
			name = bbg_common.8.over365
			value = 0
			days = 365
		}
		add_modifier = {
			name = modi_common_8_modi
			days = 365
			multiplier = -1
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = 25
			}
		}
	}
	option = {
		name = bbg_common.8.b
		set_global_variable = {
			name = bbg_common.8.over365
			value = 0
			days = 365
		}
		add_modifier = {
			name = modi_common_8_modi
			days = 365
			multiplier = 1
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = -15
			}
		}
	}
	option = {
		name = bbg_common.8.c
		set_global_variable = {
			name = bbg_common.8.over365
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = 30
			}
		}
	}
}

#环保运动与工业发展的冲突 (短版本)
bbg_common.9_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.9.t
	desc = bbg_common.9.d_short
	duration = 3
	option = {
		name = bbg_common.9.a
		set_global_variable = {
			name = bbg_common.9.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 4
			}
		}
		add_modifier = {
			name = bbg_common_environmental_pioneer_modifier
			days = 1825
		}
	}
	option = {
		name = bbg_common.9.b
		set_global_variable = {
			name = bbg_common.9.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_treasury = -2000
		add_modifier = {
			name = bbg_common_compromise_governance_modifier
			days = 1825
		}
	}
	option = {
		name = bbg_common.9.c
		set_global_variable = {
			name = bbg_common.9.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		add_modifier = {
			name = bbg_common_industrial_supremacy_modifier
			days = 1825
		}
	}
}

#实业家-军队 军工复合体的诱惑 (短版本)
bbg_common.10_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.10.t
	desc = bbg_common.10.d_short
	option = {
		name = bbg_common.10.a
		set_global_variable = {
			name = bbg_common.10.over365
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 3
				days = 730
			}
		}
		add_investment_pool = 5000
		add_modifier = {
			name = bbg_common_building_production_cost_increase
			days = 365
		}
	}
	option = {
		name = bbg_common.10.b
		set_global_variable = {
			name = bbg_common.10.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 2
				days = 365
			}
		}
		add_modifier = {
			name = bbg_common_industrial_supremacy_modifier
			days = 1095
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -4
				days = 730
			}
		}
	}
	option = {
		name = bbg_common.10.c
		set_global_variable = {
			name = bbg_common.10.over365
			value = 0
			days = 365
		}
		add_treasury = -8000
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 365
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_10
			value = 0
		}
	}
}

# 知识分子-小市民 教育世俗化改革 短版本
bbg_common.11_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.11.t
	desc = bbg_common.11.d_short
	trigger = {
		OR = {
			has_law = law_type:law_public_schools
			has_law = law_type:law_private_schools
		}
	}
	option = {
		name = bbg_common.11.a
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 1825
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -3
				days = 730
			}
		}
		add_modifier = {
			name = embrace_the_age_of_reason_modifier
			multiplier = 25
			days = 1825
		}
		set_global_variable = {
			name = bbg_global_cooldown_11
			value = 0
		}
	}
	option = {
		name = bbg_common.11.b
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 3
				days = 1825
			}
		}
		add_modifier = {
			name = bbg_common_compromise_governance_modifier
			days = 1825
		}
		set_global_variable = {
			name = bbg_global_cooldown_11
			value = 0
		}
	}
	option = {
		name = bbg_common.11.c
		add_modifier = {
			name = education_pluralism_modifier
			days = 1095
		}
		add_modifier = {
			name = bbg_common_environmental_pioneer_modifier
			days = 365
		}
		set_global_variable = {
			name = bbg_global_cooldown_11
			value = 0
		}
	}
}

# 工会-虔信者 安息日工作权 短版本
bbg_common.12_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.12.t
	desc = bbg_common.12.d_short
	trigger = {
		has_law = law_type:law_professional_army
		any_scope_state = {
			has_building = building_textile_mills
			state_has_building_levels = 10
		}
	}
	option = {
		name = bbg_common.12.a
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 5
				days = 1825
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -4
				days = 730
			}
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.15
		}
		set_global_variable = {
			name = bbg_global_cooldown_12
			value = 0
		}
	}
	option = {
		name = bbg_common.12.b
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 1825
			}
		}
		add_modifier = {
			name = bbg_common_compromise_governance_modifier
			days = 1095
		}
		add_radicals = {
			pop_type = laborers
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_12
			value = 0
		}
	}
	option = {
		name = bbg_common.12.c
		add_treasury = -5000
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 1
				days = 365
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 1
				days = 365
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_12
			value = 0
		}
	}
}

# 工会 vs. 实业家 - 工资纠纷升级 短版本
bbg_common.13_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.13.t
	desc = bbg_common.13.d_short
	trigger = {
		has_technology_researched = railways
		ig:ig_trade_unions = {
			is_powerful = yes
		}
		ig:ig_industrialists = {
			is_powerful = yes
		}
	}
	immediate = {
		add_modifier = {
			name = wage_dispute_modifier
			days = 365
		}
	}
	duration = 3
	option = {
		name = bbg_common.13.a
		custom_tooltip = bbg_common.13.a.tt
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 5
			}
		}
		add_radicals = {
			pop_type = laborers
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_13
			value = 0
		}
	}
	option = {
		name = bbg_common.13.b
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -4
			}
		}
		add_investment_pool = 2000
		set_global_variable = {
			name = bbg_global_cooldown_13
			value = 0
		}
	}
	option = {
		name = bbg_common.13.c
		add_treasury = -3000
		add_loyalists = {
			pop_type = machinists
			value = 0.05
		}
		set_global_variable = {
			name = bbg_global_cooldown_13
			value = 0
		}
	}
}

# 地主 vs. 农民 - 土地改革抗议 短版本
bbg_common.14_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.14.t
	desc = bbg_common.14.d_short
	trigger = {
		has_law = law_type:law_tenant_farmers
		ig:ig_rural_folk = {
			is_powerful = yes
		}
	}
	immediate = {
		random_scope_state = {
			limit = {
				has_building = building_rye_farm
			}
			save_scope_as = protest_state
		}
		add_modifier = {
			name = land_protest_modifier
			days = 730
		}
	}
	duration = 3
	option = {
		name = bbg_common.14.a
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 4
			}
		}
		add_radicals = {
			pop_type = farmers
			value = 0.15
		}
		set_global_variable = {
			name = bbg_global_cooldown_14
			value = 0
		}
	}
	option = {
		name = bbg_common.14.b
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = -5
			}
		}
		scope:protest_state = {
			add_modifier = {
				name = land_reform_bonus
				days = 365
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_14
			value = 0
		}
	}
	option = {
		name = bbg_common.14.c
		add_treasury = -4000
		add_loyalists = {
			value = 0.05
		}
		set_global_variable = {
			name = bbg_global_cooldown_14
			value = 0
		}
	}
}

# 知识分子 vs. 虔信者 - 教育内容争议 短版本
bbg_common.15_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.15.t
	desc = bbg_common.15.d_short
	trigger = {
		OR = {
			has_law = law_type:law_public_schools
			has_law = law_type:law_private_schools
		}
		ig:ig_intelligentsia = {
			is_marginal = no
		}
		ig:ig_devout = {
			is_marginal = no
		}
	}
	immediate = {
		add_modifier = {
			name = education_dispute_modifier
			days = 365
		}
	}
	duration = 3
	option = {
		name = bbg_common.15.a
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 4
			}
		}
		add_enactment_modifier = {
			name = secular_education_boost
			multiplier = 1
		}
		set_global_variable = {
			name = bbg_global_cooldown_15
			value = 0
		}
	}
	option = {
		name = bbg_common.15.b
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 4
			}
		}
		add_radicals = {
			pop_type = academics
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_15
			value = 0
		}
	}
	option = {
		name = bbg_common.15.c
		add_modifier = {
			name = education_compromise_modifier
			days = 1825
			multiplier = 1
		}
		add_loyalists = {
			pop_type = clergymen
			value = 0.05
		}
		set_global_variable = {
			name = bbg_global_cooldown_15
			value = 0
		}
	}
}

# 军队 vs. 小市民 - 征兵负担分歧 短版本
bbg_common.16_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.16.t
	desc = bbg_common.16.d_short
	trigger = {
		has_law = law_type:law_professional_army
		is_at_war = yes
	}
	immediate = {
		random_neighbouring_state = {
			owner = {
				save_scope_as = neighbor_country
			}
		}
		add_modifier = {
			name = conscription_burden_modifier
			days = 365
		}
	}
	duration = 3
	option = {
		name = bbg_common.16.a
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		add_radicals = {
			pop_type = shopkeepers
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_16
			value = 0
		}
	}
	option = {
		name = bbg_common.16.b
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -4
			}
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = 20
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_16
			value = 0
		}
	}
	option = {
		name = bbg_common.16.c
		add_treasury = -5000
		add_loyalists = {
			pop_type = shopkeepers
			value = 0.05
		}
		set_global_variable = {
			name = bbg_global_cooldown_16
			value = 0
		}
	}
}

# 知识分子-军队 军事学院的现代化 短版本
bbg_common.17_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.17.t
	desc = bbg_common.17.d_short
	trigger = {
		has_technology_researched = military_statistics
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		ig:ig_armed_forces = {
			is_marginal = no
		}
		any_scope_state = {
			has_building = building_university
		}
	}
	duration = 3
	
	option = {
		name = bbg_common.17.a
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = military_modernization_modifier
			days = 1825
		}
		add_treasury = -3000
		set_global_variable = {
			name = bbg_global_cooldown_17
			value = 0
		}
	}
	
	option = {
		name = bbg_common.17.b
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		add_modifier = {
			name = military_tradition_modifier
			days = 1095
		}
		set_global_variable = {
			name = bbg_global_cooldown_17
			value = 0
		}
	}
	
	option = {
		name = bbg_common.17.c
		add_treasury = -1500
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = military_academic_cooperation_modifier
			days = 1460
		}
		set_global_variable = {
			name = bbg_global_cooldown_17
			value = 0
		}
	}
}

# 小市民-虔信者 商业道德争议 (短版本)
bbg_common.18_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.18.t
	desc = bbg_common.18.d_short
	trigger = {
		ig:ig_petty_bourgeoisie = {
			is_powerful = yes
		}
		ig:ig_devout = {
			is_marginal = no
		}
	}
	duration = 3
	
	option = {
		name = bbg_common.18.a
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 5
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		add_modifier = {
			name = commercial_freedom_modifier
			days = 1825
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.1
		}
	}
	
	option = {
		name = bbg_common.18.b
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		add_modifier = {
			name = moral_commerce_modifier
			days = 1825
		}
		add_radicals = {
			pop_type = shopkeepers
			value = 0.15
		}
	}
	
	option = {
		name = bbg_common.18.c
		add_treasury = -4000
		add_investment_pool = 2000
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = ethical_banking_modifier
			days = 1460
		}
	}
}

# 实业家-农民 机械化农业的冲击 (短版本)
bbg_common.19_short = {
	type = country_event
	placement = root
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	title = bbg_common.19.t
	desc = bbg_common.19.d_short
	trigger = {
		has_technology_researched = mechanical_production
		ig:ig_industrialists = {
			is_powerful = yes
		}
		ig:ig_rural_folk = {
			is_powerful = yes
		}
		any_scope_state = {
			OR = {
				has_building = building_rye_farm
				has_building = building_wheat_farm
				has_building = building_rice_farm
			}
			state_has_building_levels = 15
		}
	}
	duration = 3
	
	immediate = {
		random_scope_state = {
			limit = {
				OR = {
					has_building = building_rye_farm
					has_building = building_wheat_farm
					has_building = building_rice_farm
				}
				state_has_building_levels = 15
			}
			save_scope_as = agricultural_state
		}
	}
	
	option = {
		name = bbg_common.19.a
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		scope:agricultural_state = {
			add_modifier = {
				name = agricultural_mechanization_boost
				days = 1825
			}
		}
		add_radicals = {
			pop_type = farmers
			value = 0.2
		}
	}
	
	option = {
		name = bbg_common.19.b
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = traditional_farming_protection
			days = 1095
		}
	}
	
	option = {
		name = bbg_common.19.c
		add_treasury = -8000
		scope:agricultural_state = {
			add_modifier = {
				name = agricultural_transition_support
				days = 1460
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
	}
}

# 知识分子-虔信者 达尔文的阴影 (短版本)
bbg_common.20_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.20.t
	desc = bbg_common.20.d_short
	trigger = {
		has_technology_researched = empiricism
		OR = {
			has_law = law_type:law_public_schools
			has_law = law_type:law_private_schools
		}
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		ig:ig_devout = {
			is_marginal = no
		}
	}
	duration = 3
	
	option = {
		name = bbg_common.20.a
		ig:ig_intelligentsia = {
			add_modifier = {
				name = scientific_freedom_boost
				days = 1825
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = religious_authority_undermined
				days = 1825
			}
		}
		add_modifier = {
			name = secular_education_advancement
			days = 1825
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.15
		}
	}
	
	option = {
		name = bbg_common.20.b
		ig:ig_devout = {
			add_modifier = {
				name = religious_education_reinforced
				days = 1825
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = academic_freedom_restricted
				days = 1825
			}
		}
		add_modifier = {
			name = religious_curriculum_emphasis
			days = 1825
		}
	}
	
	option = {
		name = bbg_common.20.c
		add_treasury = -3000
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = balanced_curriculum_approach
			days = 1460
		}
	}
}


# 实业家-工会 机器与人力的冲突 (短版本)
bbg_common.21_short = {
	type = country_event
	placement = root
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	title = bbg_common.21.t
	desc = bbg_common.21.d_short
	trigger = {
		has_technology_researched = mechanical_production
		ig:ig_industrialists = {
			is_powerful = yes
		}
		ig:ig_trade_unions = {
			is_powerful = yes
		}
		any_scope_state = {
			has_building = building_textile_mills
			state_has_building_levels = 15
		}
	}
	duration = 3
	
	immediate = {
		random_scope_state = {
			limit = {
				has_building = building_textile_mills
				state_has_building_levels = 15
			}
			save_scope_as = industrial_state
		}
	}
	
	option = {
		name = bbg_common.21.a
		ig:ig_industrialists = {
			add_modifier = {
				name = technological_advancement_boost
				days = 1825
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = worker_displacement_concern
				days = 1825
			}
		}
		scope:industrial_state = {
			add_modifier = {
				name = industrial_modernization
				days = 1825
			}
		}
		add_radicals = {
			pop_type = machinists
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_21
			value = 0
		}
	}
	
	option = {
		name = bbg_common.21.b
		ig:ig_trade_unions = {
			add_modifier = {
				name = worker_protection_secured
				days = 1825
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = innovation_restricted
				days = 1825
			}
		}
		add_modifier = {
			name = technological_conservatism
			days = 1825
		}
		set_global_variable = {
			name = bbg_global_cooldown_21
			value = 0
		}
	}
	
	option = {
		name = bbg_common.21.c
		add_treasury = -5000
		add_modifier = {
			name = worker_retraining_program
			days = 1825
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 1
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_21
			value = 0
		}
	}
}

# 地主-小市民 城市的边界 (短版本)
bbg_common.22_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.22.t
	desc = bbg_common.22.d_short
	trigger = {
		has_technology_researched = railways
		ig:ig_landowners = {
			is_powerful = yes
		}
		ig:ig_petty_bourgeoisie = {
			is_powerful = yes
		}
		any_scope_state = {
			has_building = building_urban_center
			state_region_has_trait = resource_arable_land
		}
	}
	duration = 3
	
	immediate = {
		random_scope_state = {
			limit = {
				has_building = building_urban_center
				state_region_has_trait = resource_arable_land
			}
			save_scope_as = expansion_state
		}
	}
	
	option = {
		name = bbg_common.22.a
		scope:expansion_state = {
			add_modifier = {
				name = urban_expansion_boost
				days = 1825
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = commercial_opportunity_expansion
				days = 1825
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = agricultural_land_loss
				days = 1825
			}
		}
		add_radicals = {
			pop_type = farmers
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_22
			value = 0
		}
	}
	
	option = {
		name = bbg_common.22.b
		ig:ig_landowners = {
			add_modifier = {
				name = agricultural_protection_secured
				days = 1825
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = commercial_growth_limited
				days = 1825
			}
		}
		add_modifier = {
			name = rural_preservation_policy
			days = 1460
		}
		set_global_variable = {
			name = bbg_global_cooldown_22
			value = 0
		}
	}
	
	option = {
		name = bbg_common.22.c
		add_treasury = -6000
		scope:expansion_state = {
			add_modifier = {
				name = planned_urban_growth
				days = 2190
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_22
			value = 0
		}
	}
}

# 军队-知识分子 军事教育的分歧 (短版本)
bbg_common.23_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.23.t
	desc = bbg_common.23.d_short
	trigger = {
		has_technology_researched = military_statistics
		ig:ig_armed_forces = {
			is_powerful = yes
		}
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		any_scope_state = {
			has_building = building_university
		}
	}
	duration = 3
	
	option = {
		name = bbg_common.23.a
		ig:ig_armed_forces = {
			add_modifier = {
				name = military_doctrine_emphasis
				days = 1825
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = academic_freedom_restricted
				days = 1095
			}
		}
		add_modifier = {
			name = practical_military_education
			days = 1825
		}
		add_radicals = {
			pop_type = academics
			value = 0.1
		}
		set_global_variable = {
			name = bbg_global_cooldown_23
			value = 0
		}
	}
	
	option = {
		name = bbg_common.23.b
		ig:ig_intelligentsia = {
			add_modifier = {
				name = scientific_military_approach
				days = 1825
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = traditional_discipline_questioned
				days = 1095
			}
		}
		add_modifier = {
			name = theoretical_military_studies
			days = 1825
		}
		set_global_variable = {
			name = bbg_global_cooldown_23
			value = 0
		}
	}
	
	option = {
		name = bbg_common.23.c
		add_treasury = -4000
		add_modifier = {
			name = balanced_military_education
			days = 1825
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_23
			value = 0
		}
	}
}

# 工会-实业家 工作时间的争端 (短版本)
bbg_common.24_short = {
	type = country_event
	placement = root
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	title = bbg_common.24.t
	desc = bbg_common.24.d_short
	trigger = {
		has_technology_researched = mechanical_production
		ig:ig_trade_unions = {
			is_powerful = yes
		}
		ig:ig_industrialists = {
			is_powerful = yes
		}
		any_scope_state = {
			OR = {
				has_building = building_textile_mills
				has_building = building_motor_industry
			}
			state_has_building_levels = 12
		}
	}
	duration = 3
	
	immediate = {
		random_scope_state = {
			limit = {
				OR = {
					has_building = building_textile_mills
					has_building = building_motor_industry
				}
				state_has_building_levels = 12
			}
			save_scope_as = industrial_center
		}
	}
	
	option = {
		name = bbg_common.24.a
		ig:ig_trade_unions = {
			add_modifier = {
				name = worker_rights_victory
				days = 1825
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = production_efficiency_reduced
				days = 1825
			}
		}
		add_modifier = {
			name = eight_hour_workday
			days = 1825
		}
		scope:industrial_center = {
			add_modifier = {
				name = reduced_working_hours
				days = 1825
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_24
			value = 0
		}
	}
	
	option = {
		name = bbg_common.24.b
		ig:ig_industrialists = {
			add_modifier = {
				name = production_maximization
				days = 1825
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = worker_exploitation_concern
				days = 1825
			}
		}
		add_modifier = {
			name = extended_working_hours
			days = 1095
		}
		add_radicals = {
			pop_type = machinists
			value = 0.15
		}
		set_global_variable = {
			name = bbg_global_cooldown_24
			value = 0
		}
	}
	
	option = {
		name = bbg_common.24.c
		add_treasury = -3000
		add_modifier = {
			name = flexible_working_arrangements
			days = 1460
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_24
			value = 0
		}
	}
}

# 虔信者-知识分子 科学与信仰的边界 (短版本)
bbg_common.25_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.25.t
	desc = bbg_common.25.d_short
	trigger = {
		has_technology_researched = empiricism
		ig:ig_devout = {
			is_powerful = yes
		}
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		any_scope_state = {
			has_building = building_university
		}
	}
	duration = 3
	
	option = {
		name = bbg_common.25.a
		ig:ig_devout = {
			add_modifier = {
				name = religious_authority_defended
				days = 1825
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = scientific_inquiry_limited
				days = 1825
			}
		}
		add_modifier = {
			name = faith_based_curriculum
			days = 1825
		}
		add_radicals = {
			pop_type = academics
			value = 0.12
		}
		set_global_variable = {
			name = bbg_global_cooldown_25
			value = 0
		}
	}
	
	option = {
		name = bbg_common.25.b
		ig:ig_intelligentsia = {
			add_modifier = {
				name = scientific_freedom_secured
				days = 1825
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = religious_influence_diminished
				days = 1825
			}
		}
		add_modifier = {
			name = secular_research_advancement
			days = 1825
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.12
		}
		set_global_variable = {
			name = bbg_global_cooldown_25
			value = 0
		}
	}
	
	option = {
		name = bbg_common.25.c
		add_treasury = -5000
		add_modifier = {
			name = harmonious_knowledge_pursuit
			days = 1825
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		set_global_variable = {
			name = bbg_global_cooldown_25
			value = 0
		}
	}
}
# 实业家-小市民-地主 银行危机与金融恐慌 (短版本)
bbg_common.26_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.26.t
	desc = bbg_common.26.d_short
	trigger = {
		has_technology_researched = banking
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.26.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.26.a  # "政府救助银行"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = banking_crisis_government_bailout
			days = 1095
		}
		add_treasury = -2000
	}

	option = {
		name = bbg_common.26.b  # "让市场自我调节"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = financial_crisis_impact
				days = 1095
				multiplier = -2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = financial_crisis_impact
				days = 1095
				multiplier = -3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = financial_crisis_impact
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = banking_crisis_market_regulation
			days = 730
		}
	}

	option = {
		name = bbg_common.26.c  # "临时国有化银行"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = banking_crisis_nationalization
			days = 1095
		}
		add_treasury = -1000
	}
}

# 实业家-小市民-军队 殖民地贸易垄断争议 (短版本)
bbg_common.27_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.27.t
	desc = bbg_common.27.d_short
	trigger = {
		has_technology_researched = navigation
		any_scope_state = {
			OR = {
				has_building = building_port
				has_building = building_trade_center
			}
		}
		NOT = {
			has_global_variable = bbg_common.27.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.27.a  # "维护贸易垄断"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = colonial_trade_monopoly
			days = 1095
		}
	}

	option = {
		name = bbg_common.27.b  # "开放自由贸易"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = free_trade_policy
			days = 1095
		}
	}

	option = {
		name = bbg_common.27.c  # "军事管制贸易"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = military_trade_control
			days = 1095
		}
		add_treasury = -500
	}
}

# 地主-实业家-农民 货币制度改革辩论 (短版本)
bbg_common.28_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.28.t
	desc = bbg_common.28.d_short
	trigger = {
		has_technology_researched = currency_standards
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.28.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.28.a  # "采用金本位"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = gold_standard_benefit
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.28.b  # "维持银本位"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.28.c  # "混合货币制度"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -800
	}
}

# 政府-各利益集团 国际债务危机 (短版本)
bbg_common.29_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.29.t
	desc = bbg_common.29.d_short
	trigger = {
		has_technology_researched = international_trade
		NOT = {
			has_global_variable = bbg_common.29.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.29.a
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -1000
	}

	option = {
		name = bbg_common.29.b
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -500
	}

	option = {
		name = bbg_common.29.c
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -1500
	}
}

# 知识分子-实业家-军队 外国投资与主权争议 (短版本)
bbg_common.30_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.30.t
	desc = bbg_common.30.d_short
	trigger = {
		has_technology_researched = international_trade
		NOT = {
			has_global_variable = bbg_common.30.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.30.a
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -600
	}

	option = {
		name = bbg_common.30.b
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -300
	}

	option = {
		name = bbg_common.30.c
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		# 基本效果 - 需要根据原事件调整
		add_treasury = -900
	}
}

# 知识分子-实业家-虔信者 科学与宗教边界争议 (短版本)
bbg_common.31_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.31.t
	desc = bbg_common.31.d_short
	trigger = {
		has_technology_researched = empiricism
		any_scope_state = {
			has_building = building_university
		}
		NOT = {
			has_global_variable = bbg_common.31.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.31.a
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.31.b
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.31.c
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -500
	}
}

# 实业家-小市民-农民 农业机械化危机 (短版本)
bbg_common.32_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.32.t
	desc = bbg_common.32.d_short
	trigger = {
		has_technology_researched = mechanical_production
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.32.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.32.a
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = forced_mechanization_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.32.b
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = traditional_agriculture_protection_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.32.c
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = gradual_mechanization_modifier
			days = 1095
		}
		add_treasury = -800
	}
}

# 知识分子-虔信者-实业家 达尔文的阴影 (短版本)
bbg_common.33_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.33.t
	desc = bbg_common.33.d_short
	trigger = {
		has_technology_researched = empiricism
		any_scope_state = {
			has_building = building_university
		}
		NOT = {
			has_global_variable = bbg_common.33.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.33.a
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.33.b
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.33.c
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -300
	}
}

# 实业家-工会-小市民 机器的崛起 (短版本)
bbg_common.34_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.34.t
	desc = bbg_common.34.d_short
	trigger = {
		has_technology_researched = mechanical_production
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.34.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.34.a
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.34.b
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
	}

	option = {
		name = bbg_common.34.c
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -1000
	}
}

# 实业家-小市民-地主 城市边界扩张 (短版本)
bbg_common.35_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.35.t
	desc = bbg_common.35.d_short
	trigger = {
		has_technology_researched = urban_planning
		any_scope_state = {
			has_building = building_urban_center
		}
		NOT = {
			has_global_variable = bbg_common.35.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.35.a
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
	}

	option = {
		name = bbg_common.35.b
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.35.c
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -600
	}
}

# 工会-实业家-军队 蒸汽时代劳工抗议 (短版本)
bbg_common.36_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.36.t
	desc = bbg_common.36.d_short
	trigger = {
		has_technology_researched = mechanical_production
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.36.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.36.a
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.36.b
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
	}

	option = {
		name = bbg_common.36.c
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_treasury = -800
	}
}

# 实业家-小市民-军队 铁路建设的社会影响 (短版本)
bbg_common.37_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.37.t
	desc = bbg_common.37.d_short
	trigger = {
		has_technology_researched = railways
		any_scope_state = {
			has_building = building_railway
		}
		NOT = {
			has_global_variable = bbg_common.37.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.37.a
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.37.b
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.37.c
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -700
	}
}

# 知识分子-虔信者-实业家 科学与信仰边界 (短版本)
bbg_common.38_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.38.t
	desc = bbg_common.38.d_short
	trigger = {
		has_technology_researched = empiricism
		any_scope_state = {
			has_building = building_university
		}
		NOT = {
			has_global_variable = bbg_common.38.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.38.a
		set_global_variable = {
			name = bbg_common.38.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.38.b
		set_global_variable = {
			name = bbg_common.38.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.38.c
		set_global_variable = {
			name = bbg_common.38.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_treasury = -400
	}
}

# 批量模板：事件39-50的短版本（基本结构）
# 注意：这些是简化的模板，需要根据原事件进行详细调整

# 事件39短版本
bbg_common.39_short = {
	type = country_event
	placement = root
	event_image = { video = "asia_union_leader" }
	title = bbg_common.39.t
	desc = bbg_common.39.d_short
	trigger = {
		has_technology_researched = international_trade
		NOT = { has_global_variable = bbg_common.39.cooldown }
	}
	duration = 3
	option = {
		name = bbg_common.39.a
		set_global_variable = { name = bbg_common.39.cooldown value = 0 days = 365 }
		add_treasury = -500
	}
	option = {
		name = bbg_common.39.b
		set_global_variable = { name = bbg_common.39.cooldown value = 0 days = 365 }
		add_treasury = -300
	}
	option = {
		name = bbg_common.39.c
		set_global_variable = { name = bbg_common.39.cooldown value = 0 days = 365 }
		add_treasury = -800
	}
}

# 事件40短版本
bbg_common.40_short = {
	type = country_event
	placement = root
	event_image = { video = "europenorthamerica_political_extremism" }
	title = bbg_common.40.t
	desc = bbg_common.40.d_short
	trigger = {
		has_technology_researched = empiricism
		NOT = { has_global_variable = bbg_common.40.cooldown }
	}
	duration = 3
	option = {
		name = bbg_common.40.a
		set_global_variable = { name = bbg_common.40.cooldown value = 0 days = 365 }
		add_treasury = -600
	}
	option = {
		name = bbg_common.40.b
		set_global_variable = { name = bbg_common.40.cooldown value = 0 days = 365 }
		add_treasury = -400
	}
	option = {
		name = bbg_common.40.c
		set_global_variable = { name = bbg_common.40.cooldown value = 0 days = 365 }
		add_treasury = -1000
	}
}

# 工会-实业家-政府 养老金制度提案 (短版本)
bbg_common.41_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.41.t
	desc = bbg_common.41.d_short
	trigger = {
		has_technology_researched = labor_movement
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.41.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.41.a  # "建立养老金制度"
		set_global_variable = {
			name = bbg_common.41.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -2
			}
		}
		add_modifier = {
			name = pension_system_modifier
			days = 1825
		}
		add_treasury = -1500
	}

	option = {
		name = bbg_common.41.b  # "维持家庭责任"
		set_global_variable = {
			name = bbg_common.41.cooldown
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = traditional_family_responsibility_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.41.c  # "创建自愿储蓄计划"
		set_global_variable = {
			name = bbg_common.41.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = voluntary_savings_program_modifier
			days = 1095
		}
		add_treasury = -800
	}
}

# 实业家-小市民-知识分子 电力基础设施 (短版本)
bbg_common.42_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.42.t
	desc = bbg_common.42.d_short
	trigger = {
		has_technology_researched = electrical_generation
		any_scope_state = {
			has_building = building_urban_center
		}
		NOT = {
			has_global_variable = bbg_common.42.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.42.a  # "投资电力基础设施"
		set_global_variable = {
			name = bbg_common.42.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		add_modifier = {
			name = electrical_infrastructure_investment_modifier
			days = 1825
		}
		add_treasury = -2000
	}

	option = {
		name = bbg_common.42.b  # "维持煤气照明"
		set_global_variable = {
			name = bbg_common.42.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = gas_lighting_preservation_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.42.c  # "支持渐进电气化"
		set_global_variable = {
			name = bbg_common.42.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = gradual_electrification_modifier
			days = 1460
		}
		add_treasury = -1000
	}
}

# 工会-实业家-政府 劳工工会认可 (短版本)
bbg_common.43_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.43.t
	desc = bbg_common.43.d_short
	trigger = {
		has_technology_researched = labor_movement
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.43.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.43.a  # "承认劳工工会"
		set_global_variable = {
			name = bbg_common.43.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 5
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		add_modifier = {
			name = labor_union_recognition_modifier
			days = 1825
		}
	}

	option = {
		name = bbg_common.43.b  # "反对工会组织"
		set_global_variable = {
			name = bbg_common.43.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		add_modifier = {
			name = union_opposition_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.43.c  # "允许有限工会权利"
		set_global_variable = {
			name = bbg_common.43.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = limited_union_rights_modifier
			days = 1460
		}
		add_treasury = -600
	}
}

# 知识分子-实业家-虔信者 文化保护运动 (短版本)
bbg_common.44_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.44.t
	desc = bbg_common.44.d_short
	trigger = {
		has_technology_researched = nationalism
		any_scope_state = {
			has_building = building_university
		}
		NOT = {
			has_global_variable = bbg_common.44.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.44.a  # "支持文化保护"
		set_global_variable = {
			name = bbg_common.44.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = cultural_preservation_modifier
			days = 1460
		}
		add_treasury = -800
	}

	option = {
		name = bbg_common.44.b  # "拥抱文化现代化"
		set_global_variable = {
			name = bbg_common.44.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = cultural_modernization_modifier
			days = 1460
		}
	}

	option = {
		name = bbg_common.44.c  # "平衡传统与进步"
		set_global_variable = {
			name = bbg_common.44.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = cultural_balance_modifier
			days = 1095
		}
		add_treasury = -500
	}
}

# 知识分子-虔信者-实业家 医学突破争议 (短版本)
bbg_common.45_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.45.t
	desc = bbg_common.45.d_short
	trigger = {
		has_technology_researched = medical_degrees
		any_scope_state = {
			has_building = building_university
		}
		NOT = {
			has_global_variable = bbg_common.45.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.45.a  # "支持医学创新"
		set_global_variable = {
			name = bbg_common.45.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = medical_innovation_support_modifier
			days = 1460
		}
		add_treasury = -600
	}

	option = {
		name = bbg_common.45.b  # "尊重宗教关切"
		set_global_variable = {
			name = bbg_common.45.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = religious_medical_concerns_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.45.c  # "建立医学伦理委员会"
		set_global_variable = {
			name = bbg_common.45.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = medical_ethics_board_modifier
			days = 1460
		}
		add_treasury = -400
	}
}
# 实业家-工会-小市民 交通革命 (短版本)
bbg_common.46_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.46.t
	desc = bbg_common.46.d_short
	trigger = {
		has_technology_researched = railways
		any_scope_state = {
			has_building = building_railway
		}
		NOT = {
			has_global_variable = bbg_common.46.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.46.a  # "加速交通发展"
		set_global_variable = {
			name = bbg_common.46.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = transportation_acceleration_modifier
			days = 1460
		}
		add_treasury = -1200
	}

	option = {
		name = bbg_common.46.b  # "保护传统交通"
		set_global_variable = {
			name = bbg_common.46.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = traditional_transportation_protection_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.46.c  # "提供过渡援助"
		set_global_variable = {
			name = bbg_common.46.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = transportation_transition_assistance_modifier
			days = 1095
		}
		add_treasury = -800
	}
}

# 军队-知识分子-实业家 国际外交危机 (短版本)
bbg_common.47_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.47.t
	desc = bbg_common.47.d_short
	trigger = {
		has_technology_researched = military_statistics
		any_scope_state = {
			has_building = building_barracks
		}
		NOT = {
			has_global_variable = bbg_common.47.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.47.a  # "准备军事行动"
		set_global_variable = {
			name = bbg_common.47.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = military_preparation_modifier
			days = 730
		}
		add_treasury = -1000
	}

	option = {
		name = bbg_common.47.b  # "寻求外交解决"
		set_global_variable = {
			name = bbg_common.47.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = diplomatic_solution_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.47.c  # "寻求国际调解"
		set_global_variable = {
			name = bbg_common.47.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = international_mediation_modifier
			days = 1095
		}
		add_treasury = -600
	}
}

# 工会-实业家-政府 社会保险辩论 (短版本)
bbg_common.48_short = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.48.t
	desc = bbg_common.48.d_short
	trigger = {
		has_technology_researched = labor_movement
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.48.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.48.a  # "实施社会保险"
		set_global_variable = {
			name = bbg_common.48.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 4
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = social_insurance_system_modifier
			days = 1825
		}
		add_treasury = -1800
	}

	option = {
		name = bbg_common.48.b  # "维持个人责任"
		set_global_variable = {
			name = bbg_common.48.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		add_modifier = {
			name = individual_responsibility_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.48.c  # "创建有限安全网"
		set_global_variable = {
			name = bbg_common.48.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = limited_safety_net_modifier
			days = 1460
		}
		add_treasury = -900
	}
}

# 实业家-知识分子-政府 技术专利战争 (短版本)
bbg_common.49_short = {
	type = country_event
	placement = root
	event_image = {
		video = "europenorthamerica_political_extremism"
	}
	title = bbg_common.49.t
	desc = bbg_common.49.d_short
	trigger = {
		has_technology_researched = patent_rights
		any_scope_state = {
			has_building = building_tooling_workshops
		}
		NOT = {
			has_global_variable = bbg_common.49.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.49.a  # "加强专利执行"
		set_global_variable = {
			name = bbg_common.49.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = patent_enforcement_strengthening_modifier
			days = 1460
		}
		add_treasury = -700
	}

	option = {
		name = bbg_common.49.b  # "削弱专利保护"
		set_global_variable = {
			name = bbg_common.49.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = patent_protection_weakening_modifier
			days = 1095
		}
	}

	option = {
		name = bbg_common.49.c  # "改革专利制度"
		set_global_variable = {
			name = bbg_common.49.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = patent_system_reform_modifier
			days = 1460
		}
		add_treasury = -500
	}
}


# 工会-地主-知识分子 民主改革运动 (短版本)
bbg_common.50_short = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.50.t
	desc = bbg_common.50.d_short
	trigger = {
		has_technology_researched = political_agitation
		any_scope_state = {
			has_building = building_government_administration
		}
		NOT = {
			has_global_variable = bbg_common.50.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.50.a  # "扩大民主权利"
		set_global_variable = {
			name = bbg_common.50.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1825
				multiplier = 5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
	}

	option = {
		name = bbg_common.50.b  # "维持现有制度"
		set_global_variable = {
			name = bbg_common.50.cooldown
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		# add_modifier = {
		# 	name = current_system_maintenance_modifier
		# 	days = 1095
		# }
	}

	option = {
		name = bbg_common.50.c  # "实施渐进改革"
		set_global_variable = {
			name = bbg_common.50.cooldown
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_treasury = -800
	}
}

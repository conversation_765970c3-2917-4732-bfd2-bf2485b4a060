namespace = bbg_common
#地主-农民 恶意掠夺
bbg_common.1 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.1.t
	desc = bbg_common.1.d
	flavor = bbg_common.1.f
	trigger = {
		or = {
			has_law = law_type:law_serfdom
			has_law = law_type:law_tenant_farmers
		}
		NOT = {
			has_global_variable = bbg_common.1.over365
		}
	}
	duration = 3
	option = {
		name = bbg_common.1.a
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
	option = {
		name = bbg_common.1.b
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -1000
		}
	}
	option = {
		name = bbg_common.1.c
		set_global_variable = {
			name = bbg_common.1.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 3
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -2000
		}
	}
}

#地主-资本家 善意欺骗
bbg_common.2 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.2.t
	desc = bbg_common.2.d
	flavor = bbg_common.2.f
	duration = 3
	immediate = {
		random_scope_state = {
			limit = {
				not = {
					is_capital = yes
				}
			}
			save_scope_as = state_random
		}
	}
	option = {
		name = bbg_common.2.a
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		scope:state_random = {
			create_building = {
				building = "building_university"
				level = 1
				reserves = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 365
				multiplier = 10
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -4
			}
		}
	}
	option = {
		name = bbg_common.2.b
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
	}
	option = {
		name = bbg_common.2.c1
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = 1000
		}
	}
	option = {
		name = bbg_common.2.c2
		set_global_variable = {
			name = bbg_common.2.over365
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_investment_pool = 1000
	}
}

# 工会-实业家+军队 工厂性罢工
bbg_common.3 = {
	title = bbg_common.3.ti
	desc = bbg_common.3.de
	flavor = bbg_common.3.f
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	duration = 3
	# 选项A：派遣军队镇压
	option = {
		name = bbg_common.3.a
		custom_tooltip = bbg_common.3.a.tt
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		# 检查军队支持度，如果低于-5（不满），则会产生负面效果
		if = {
			limit = {
				ig:ig_armed_forces = {
					ig_approval < -5
				}
			}
			# 后果：军队抗命
			ig:ig_armed_forces = {
				add_modifier = {
					name = bbg_ig_approval_add					# 军队支持度-3，政治力量-15%
					days = 730					# 持续2年
					multiplier = -3
				}
			}
			ig:ig_industrialists = {
				add_modifier = {
					name = bbg_ig_approval_add
					days = 730
					multiplier = 1
				}				# 实业家轻微支持
			}
			ig:ig_trade_unions = {
				add_modifier = {
					name = bbg_ig_approval_add
					days = 730
					multiplier = -5
				}				# 工会极度愤怒
			}
			add_radicals = {
				pop_type = machinists
				value = 0.1				# 10% 的技工变为激进派
			}
			add_radicals = {
				pop_type = laborers
				value = 0.1				# 10% 的劳工变为激进派
			}
		}
		else = {
			# 后果：军队忠诚执行命令
			ig:ig_armed_forces = {
				add_modifier = {
					name = bbg_ig_approval_add
					days = 730
					multiplier = 2
				}				# 军队获得支持
			}
			ig:ig_industrialists = {
				add_modifier = {
					name = bbg_ig_approval_add
					days = 730
					multiplier = 4
				}				# 实业家大力支持
			}
			ig:ig_trade_unions = {
				add_modifier = {
					name = bbg_ig_approval_add
					days = 730
					multiplier = -4
				}				# 工会非常愤怒
			}
			add_radicals = {
				pop_type = machinists
				value = 0.05				# 5% 的技工变为激进派
			}
			add_radicals = {
				pop_type = laborers
				value = 0.05				# 5% 的劳工变为激进派
			}
		}
	}
	# 选项B：出面调停
	option = {
		name = bbg_common.3.b
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		custom_tooltip = bbg_common.3.b.tt		# 使用自定义提示，因为花费是动态的
		add_modifier = {
			name = bbg_c_money_add
			days = 365
			multiplier = -1000
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}		# 实业家轻微不满
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}		# 工会轻微不满
		# 减少激进派（转化为忠诚派）
		add_loyalists = {
			pop_type = machinists
			value = 0.05			# 5% 的技工变为忠诚派
		}
		add_loyalists = {
			pop_type = laborers
			value = 0.05			# 5% 的劳工变为忠诚派
		}
	}
	# 选项C：支持工人诉求
	option = {
		name = bbg_common.3.c
		custom_tooltip = bbg_common.3.c.tt
		set_global_variable = {
			name = bbg_common.3.over365
			value = 0
			days = 365
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 5
			}			# 工会支持度飙升
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -5
			}			# 实业家极度愤怒
		}
	}
}

# 小市民-实业家 小店的黄昏
bbg_common.4 = {
	type = country_event
	placement = root
	title = bbg_common.4.t
	desc = bbg_common.4.d
	flavor = bbg_common.4.f
	event_image = {
		video = "southamerica_election"
	}
	duration = 3
	option = {
		# 进步的代价
		name = bbg_common.4.a
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 10
				days = 1825
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 1825
			}
		}
		add_radicals = {
			pop_type = shopkeepers
			value = 0.15
		}
	}
	option = {
		# 保护本土商业
		name = bbg_common.4.b
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 10
				days = 1825
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 1825
			}
		}
		add_modifier = {
			name = investment_pool_contribution_efficiency_mult_modifier
			days = 1825
		}
	}
	option = {
		# 提供商业补贴
		name = bbg_common.4.c
		add_treasury = -4000
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 15
				days = 1825
			}
		}
		add_loyalists = {
			pop_type = shopkeepers
			value = 0.05
		}
	}
}

# 知识分子-虔信者 信仰与科学
bbg_common.5 = {
	type = country_event
	placement = root
	title = bbg_common.5.t
	desc = bbg_common.5.d
	flavor = bbg_common.5.f
	event_image = {
		video = "asia_union_leader"
	}
	duration = 3
	trigger = {
		OR = {
			has_law = law_type:law_public_schools
			has_law = law_type:law_private_schools
		}
		ig:ig_devout = {
			is_marginal = no
		}
		ig:ig_intelligentsia = {
			is_marginal = no
		}
	}
	option = {
		# 拥抱理性时代
		name = bbg_common.5.a
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 2
				days = 1825
			}
		}
		add_modifier = {
			name = embrace_the_age_of_reason_modifier
			days = 1825
			multiplier = 20
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -3
				days = 1825
			}
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.2
		}
		add_enactment_modifier = {
			name = watashiwamajimen
			multiplier = 10
		}
	}
	option = {
		# 捍卫神圣传统
		name = bbg_common.5.b
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 2
				days = 1825
			}
		}
		add_enactment_modifier = {
			name = watashiwamajimen
			multiplier = 10
		}
		# add_modifier = {
		# 	name = defend_sacred_traditions_modifier
		# 	days = 1825
		# }
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -4
				days = 1825
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 1825
				multiplier = -5
			}
		}
	}
	option = {
		# 寻求折衷之道
		name = bbg_common.5.c
		add_modifier = {
			name = seek_a_compromise_modifier
			days = 1825
			multiplier = 5  # Reduced from 50
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 1825
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 1825
				multiplier = 5
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 1825
			}
			add_modifier = {
				name = bbg_ig_pop_attraction_add
				days = 1825
				multiplier = 5
			}
		}
		add_enactment_modifier = {
			name = watashiwamajimen
			multiplier = 5
		}
	}
}

# 实业家-农民 谷物法之争
bbg_common.6 = {
	type = country_event
	placement = root
	title = bbg_common.6.t
	desc = bbg_common.6.d
	flavor = bbg_common.6.f
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	duration = 3
	option = {
		# “市民必须有面包吃！”
		name = bbg_common.6.a
		add_treasury = -5000		# 花费国库进口粮食
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 1825				# 5 years
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 1825
			}
		}
		add_radicals = {
			pop_type = farmers
			value = 0.1
		}
		add_loyalists = {
			pop_type = machinists
			value = 0.05
		}
	}
	option = {
		# “保护我们的农民！”
		name = bbg_common.6.b
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 4
				days = 1825
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 1825
			}
		}
		
		add_radicals = {
			pop_type = laborers
			value = 0.1
		}
	}
	option = {
		# “用国库补贴双方。”
		name = bbg_common.6.c
		add_treasury = -10000		# 巨额花费
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 1825
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -1
				days = 1825
			}
		}
		add_loyalists = {
			value = 0.05			# 少量增加全国忠诚派
		}
	}
}

# 工会-农民 团结的代价
bbg_common.7 = {
	type = country_event
	placement = root
	title = bbg_common.7.t
	desc = bbg_common.7.d
	flavor = bbg_common.7.f
	event_image = {
		video = "africa_public_protest"
	}
	duration = 3
	option = {
		# “鼓励农民加入罢工！”
		name = bbg_common.7.a
		custom_tooltip = bbg_common.7.a.tt
		if = {
			limit = {
				ig:ig_rural_folk = {
					ig_approval > 0
				}
			}
			# 成功：农民加入
			ig:ig_trade_unions = {
				add_modifier = {
					name = bbg_ig_approval_add
					multiplier = 5
					days = 1825
				}
			}
			ig:ig_rural_folk = {
				add_modifier = {
					name = bbg_ig_approval_add
					multiplier = -2
					days = 1825
				}
			}
			add_modifier = {
				name = worker_peasant_solidarity_modifier
				days = 365
			}
		}
		else = {
			# 失败：农民拒绝
			ig:ig_trade_unions = {
				add_modifier = {
					name = bbg_ig_approval_add
					multiplier = -3
					days = 1825
				}
			}
			ig:ig_rural_folk = {
				add_modifier = {
					name = bbg_ig_approval_add
					multiplier = -3
					days = 1825
				}
			}
		}
	}
	option = {
		# “谴责工会的激进行为。”
		name = bbg_common.7.b
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -5
				days = 1825
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = 2
				days = 1825
			}
		}
		add_radicals = {
			pop_type = laborers
			value = 0.15
		}
	}
	option = {
		# “保持中立，静观其变。”
		name = bbg_common.7.c
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -2
				days = 1825
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				multiplier = -2
				days = 1825
			}
		}
	}
}
#邻国 - 意外的边界冲突
bbg_common.8 = {
	type = country_event
	placement = root
	title = bbg_common.8.t
	desc = bbg_common.8.d
	flavor = bbg_common.8.f
	event_image = {
		video = "unspecific_airplane"
	}
	duration = 3
	trigger = {
		# any_country = {
		# 	is_neighbor_of = ROOT
		# 	relation_with = {
		# 		who = ROOT
		# 		value < 0
		# 	}
		# }
	}
	immediate = {
		random_neighbouring_state = {
			limit = {
				#有兵营建筑
				has_building = building_barracks
			}
			owner = {
				save_scope_as = neighbor_country
			}
		}
	}
	option = {
		name = bbg_common.8.a
		add_modifier = {
			name = modi_common_8_modi
			days = 365
			multiplier = -1
		}
		scope:neighbor_country = {
			add_change_relations_progress  = {
				country = ROOT
				value = 25
			}
		}
	}
	option = {
		name = bbg_common.8.b
		add_modifier = {
			name = modi_common_8_modi
			days = 365
			multiplier = 1
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = -15 # 关系急剧恶化
			}
		}
	}
	option = {
		name = bbg_common.8.c
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add # 假设这个修正仍然有效
				days = 365
				multiplier = -5
			}
		}
		scope:neighbor_country = {
			add_change_relations_progress = {
				country = ROOT
				value = 30 # 关系恢复中立
			}
		}
	}
}

bbg_common.9 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election" # 可以替换为更合适的视频
	}
	title = bbg_common.9.t
	desc = bbg_common.9.d
	flavor = bbg_common.9.f
	duration = 3

	option = {
		name = bbg_common.9.a
		# 实业家：支持度大幅下降（例如：-5），可能成为激进派。
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		# 知识分子：支持度大幅上升（例如：+4）。
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 4
			}
		}
		# 小市民：支持度小幅上升（例如：+2），激进派减少。
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		# 国家激进派/忠诚派变化
		add_radicals = {
			value = 0.05 # 5%的实业家支持者变为激进派
		}
		add_loyalists = {
			value = 0.02 # 2%的小市民支持者变为忠诚派
		}
		# 经济：工业品生产成本小幅增加（例如：+5%），国家税收短期内可能减少。
		add_modifier = {
			name = bbg_common_environmental_pioneer_modifier
			days = 1825 # 5年
		}
		add_modifier = {
			name = bbg_common_building_production_cost_increase
			days = 1825
		}
		add_modifier = {
			name = bbg_common_tax_income_decrease
			days = 365
		}
	}

	option = {
		name = bbg_common.9.b
		# 实业家：支持度小幅上升（例如：+2）。
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		# 知识分子：支持度小幅下降（例如：-1）。
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		# 小市民：不满情绪可能持续，激进派小幅增加。
		add_radicals = {
			value = 0.01 # 1%的小市民支持者变为激进派
		}
		# 经济：国库支出增加（例如：-2000），工业品生产成本不变。
		add_treasury = -2000
		add_modifier = {
			name = bbg_common_compromise_governance_modifier
			days = 1825 # 5年
		}
	}

	option = {
		name = bbg_common.9.c
		# 实业家：支持度大幅上升（例如：+5），可能获得投资池额外贡献。
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -5
			}
		}
		add_modifier = {
			name = bbg_common_investment_pool_contribution_increase
			days = 1825 # 5年
		}
		# 小市民：激进派大幅增加，生活水平可能下降。
		add_radicals = {
			value = 0.05 # 5%的知识分子支持者变为激进派
		}
		add_radicals = {
			value = 0.03 # 3%的小市民支持者变为激进派
		}
		add_modifier = {
			name = bbg_common_standard_of_living_decrease
			days = 365
		}
		# 经济：工业产出小幅增加（例如：+5%），但人口健康水平持续恶化，医疗开支增加。
		add_modifier = {
			name = bbg_common_industrial_supremacy_modifier
			days = 1825 # 5年
		}
	}
}

# 实业家-军队 军工复合体的诱惑
bbg_common.10 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.10.t
    desc = bbg_common.10.d
    flavor = bbg_common.10.f
    trigger = {
        has_technology_researched = railways
    }
    option = {
        name = bbg_common.10.a
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 3
                days = 730
            }
        }
        add_investment_pool = 5000
        add_modifier = {
            name = bbg_common_building_production_cost_increase
            days = 365
        }
    }
    option = {
        name = bbg_common.10.b
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 2
                days = 365
            }
        }
        add_modifier = {
            name = bbg_common_industrial_supremacy_modifier
            days = 1095
        }
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = -4
                days = 730
            }
        }
    }
    option = {
        name = bbg_common.10.c
        add_treasury = -8000
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = -1
                days = 365
            }
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = -1
                days = 365
            }
        }
    }
}

# 知识分子-小市民 教育世俗化改革
bbg_common.11 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.11.t
    desc = bbg_common.11.d
    flavor = bbg_common.11.f
    trigger = {
        OR = {
            has_law = law_type:law_public_schools
            has_law = law_type:law_private_schools
        }
    }
    option = {
        name = bbg_common.11.a
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 4
                days = 1825
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = -3
                days = 730
            }
        }
        add_modifier = {
            name = embrace_the_age_of_reason_modifier
            multiplier = 25
            days = 1825
        }
    }
    option = {
        name = bbg_common.11.b
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 3
                days = 1825
            }
        }
        add_modifier = {
            name = bbg_common_compromise_governance_modifier
            days = 1825
        }
    }
    option = {
        name = bbg_common.11.c
        add_modifier = {
            name = education_pluralism_modifier
            days = 1095
        }
        add_modifier = {
            name = bbg_common_environmental_pioneer_modifier
            days = 365
        }
    }
}

# 工会-虔信者 安息日工作权
bbg_common.12 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.12.t
    desc = bbg_common.12.d
    flavor = bbg_common.12.f
    trigger = {
        has_law = law_type:law_professional_army
        any_scope_state = {
            has_building = building_textile_mills
            state_has_building_levels = 10
        }
    }
    option = {
        name = bbg_common.12.a
        ig:ig_trade_unions = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 5
                days = 1825
            }
        }
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = -4
                days = 730
            }
        }
        add_radicals = {
            pop_type = clergymen
            value = 0.15
        }
    }
    option = {
        name = bbg_common.12.b
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 4
                days = 1825
            }
        }
        add_modifier = {
            name = bbg_common_compromise_governance_modifier
            days = 1095
        }
        add_radicals = {
            pop_type = laborers
            value = 0.1
        }
    }
    option = {
        name = bbg_common.12.c
        add_treasury = -5000
        ig:ig_trade_unions = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 1
                days = 365
            }
        }
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                multiplier = 1
                days = 365
            }
        }
    }
}


# 工会 vs. 实业家 - 工资纠纷升级
bbg_common.13 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.13.t
    desc = bbg_common.13.d
    flavor = bbg_common.13.f
    trigger = {
        has_technology_researched = railways
        ig:ig_trade_unions = {
            is_powerful = yes
        }
        ig:ig_industrialists = {
            is_powerful = yes
        }
    }
    immediate = {
        add_modifier = {
            name = wage_dispute_modifier
            days = 365
        }
    }
    duration = 3
    option = {
        name = bbg_common.13.a
        custom_tooltip = bbg_common.13.a.tt
        ig:ig_trade_unions = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 5
            }
        }
        add_radicals = {
            pop_type = laborers
            value = 0.1
        }
    }
    option = {
        name = bbg_common.13.b
        ig:ig_trade_unions = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = -4
            }
        }
        add_investment_pool = 2000
    }
    option = {
        name = bbg_common.13.c
        add_treasury = -3000
        add_loyalists = {
            pop_type = machinists
            value = 0.05
        }
    }
}

# 地主 vs. 农民 - 土地改革抗议
bbg_common.14 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.14.t
    desc = bbg_common.14.d
    flavor = bbg_common.14.f
    trigger = {
        has_law = law_type:law_tenant_farmers
        ig:ig_rural_folk = {
            is_powerful = yes
        }
    }
    immediate = {
        random_scope_state = {
            limit = {
                has_building = building_rye_farm
            }
            save_scope_as = protest_state
        }
        add_modifier = {
            name = land_protest_modifier
            days = 730
        }
    }
    duration = 3
    option = {
        name = bbg_common.14.a
        ig:ig_landowners = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1825
                multiplier = 4
            }
        }
        add_radicals = {
            pop_type = farmers
            value = 0.15
        }
    }
    option = {
        name = bbg_common.14.b
        ig:ig_landowners = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1825
                multiplier = -5
            }
        }
        scope:protest_state = {
            add_modifier = {
                name = land_reform_bonus
                days = 365
            }
        }
    }
    option = {
        name = bbg_common.14.c
        add_treasury = -4000
        add_loyalists = {
            value = 0.05
        }
    }
}

# 知识分子 vs. 虔信者 - 教育内容争议
bbg_common.15 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.15.t
    desc = bbg_common.15.d
    flavor = bbg_common.15.f
    trigger = {
        OR = {
            has_law = law_type:law_public_schools
            has_law = law_type:law_private_schools
        }
        ig:ig_intelligentsia = {
            is_marginal = no
        }
        ig:ig_devout = {
            is_marginal = no
        }
    }
    immediate = {
        add_modifier = {
            name = education_dispute_modifier
            days = 365
        }
    }
    duration = 3
    option = {
        name = bbg_common.15.a
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1825
                multiplier = 4
            }
        }
        add_enactment_modifier = {
            name = secular_education_boost
            multiplier = 1
        }
    }
    option = {
        name = bbg_common.15.b
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1825
                multiplier = 4
            }
        }
        add_radicals = {
            pop_type = academics
            value = 0.1
        }
    }
    option = {
        name = bbg_common.15.c
        add_modifier = {
            name = education_compromise_modifier
            days = 1825
            multiplier = 1
        }
        add_loyalists = {
            pop_type = clergymen
            value = 0.05
        }
    }
}

# 军队 vs. 小市民 - 征兵负担分歧
bbg_common.16 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.16.t
    desc = bbg_common.16.d
    flavor = bbg_common.16.f
    trigger = {
        has_law = law_type:law_professional_army
        is_at_war = yes
    }
    immediate = {
        random_neighbouring_state = {
			owner = {
            	save_scope_as = neighbor_country
			}
        }
        add_modifier = {
            name = conscription_burden_modifier
            days = 365
        }
    }
    duration = 3
    option = {
        name = bbg_common.16.a
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 3
            }
        }
        add_radicals = {
            pop_type = shopkeepers
            value = 0.1
        }
    }
    option = {
        name = bbg_common.16.b
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = -4
            }
        }
        scope:neighbor_country = {
            add_change_relations_progress = {
                country = ROOT
                value = 20
            }
        }
    }
    option = {
        name = bbg_common.16.c
        add_treasury = -5000
        add_loyalists = {
            pop_type = shopkeepers
            value = 0.05
        }
    }
}

# 知识分子-军队 军事学院的现代化
bbg_common.17 = {
    type = country_event
    placement = root
    event_image = {
        video = "asia_union_leader"
    }
    title = bbg_common.17.t
    desc = bbg_common.17.d
    flavor = bbg_common.17.f
    trigger = {
        has_technology_researched = military_statistics
        ig:ig_intelligentsia = {
            is_powerful = yes
        }
        ig:ig_armed_forces = {
            is_marginal = no
        }
        any_scope_state = {
            has_building = building_university
        }
    }
    duration = 3
    
    option = {
        name = bbg_common.17.a
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 4
            }
        }
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = -3
            }
        }
        add_modifier = {
            name = military_modernization_modifier
            days = 1825
        }
        add_treasury = -3000
    }
    
    option = {
        name = bbg_common.17.b
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 3
            }
        }
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = -4
            }
        }
        add_modifier = {
            name = military_tradition_modifier
            days = 1095
        }
    }
    
    option = {
        name = bbg_common.17.c
        add_treasury = -1500
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 1
            }
        }
        ig:ig_armed_forces = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 1
            }
        }
        add_modifier = {
            name = military_academic_cooperation_modifier
            days = 1460
        }
    }
}

# 小市民-虔信者 高利贷与商业道德
bbg_common.18 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.18.t
    desc = bbg_common.18.d
    flavor = bbg_common.18.f
    trigger = {
        # OR = {
        #     has_law = law_type:law_private_banks
        #     has_law = law_type:law_national_bank
        # }
        ig:ig_petty_bourgeoisie = {
            is_powerful = yes
        }
        ig:ig_devout = {
            is_marginal = no
        }
        # any_scope_state = {
        #     has_building = building_financial_district
        # }
    }
    duration = 3
    
    option = {
        name = bbg_common.18.a
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 5
            }
        }
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = -4
            }
        }
        add_modifier = {
            name = commercial_freedom_modifier
            days = 1825
        }
        add_radicals = {
            pop_type = clergymen
            value = 0.1
        }
    }
    
    option = {
        name = bbg_common.18.b
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 4
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = -5
            }
        }
        add_modifier = {
            name = moral_commerce_modifier
            days = 1825
        }
        add_radicals = {
            pop_type = shopkeepers
            value = 0.15
        }
    }
    
    option = {
        name = bbg_common.18.c
        add_treasury = -4000
        add_investment_pool = 2000
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 2
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 1
            }
        }
        add_modifier = {
            name = ethical_banking_modifier
            days = 1460
        }
    }
}

# 实业家-农民 机械化农业的冲击
bbg_common.19 = {
    type = country_event
    placement = root
    event_image = {
        video = "africa_diplomats_negotiating"
    }
    title = bbg_common.19.t
    desc = bbg_common.19.d
    flavor = bbg_common.19.f
    trigger = {
        has_technology_researched = mechanical_production
        ig:ig_industrialists = {
            is_powerful = yes
        }
        ig:ig_rural_folk = {
            is_powerful = yes
        }
        any_scope_state = {
            OR = {
                has_building = building_rye_farm
                has_building = building_wheat_farm
                has_building = building_rice_farm
            }
            state_has_building_levels = 15
        }
    }
    duration = 3
    
    immediate = {
        random_scope_state = {
            limit = {
                OR = {
                    has_building = building_rye_farm
                    has_building = building_wheat_farm
                    has_building = building_rice_farm
                }
                state_has_building_levels = 15
            }
            save_scope_as = agricultural_state
        }
    }
    
    option = {
        name = bbg_common.19.a
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1460
                multiplier = 5
            }
        }
        ig:ig_rural_folk = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1460
                multiplier = -6
            }
        }
        scope:agricultural_state = {
            add_modifier = {
                name = forced_mechanization_modifier
                days = 1825
            }
        }
        add_radicals = {
            pop_type = farmers
            value = 0.2
        }
        add_radicals = {
            pop_type = peasants
            value = 0.15
        }
    }
    
    option = {
        name = bbg_common.19.b
        ig:ig_rural_folk = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1460
                multiplier = 4
            }
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1460
                multiplier = -4
            }
        }
        add_modifier = {
            name = traditional_agriculture_protection_modifier
            days = 1095
        }
    }
    
    option = {
        name = bbg_common.19.c
        add_treasury = -6000
        scope:agricultural_state = {
            add_modifier = {
                name = gradual_mechanization_modifier
                days = 2190
            }
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 1
            }
        }
        ig:ig_rural_folk = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 730
                multiplier = 2
            }
        }
        add_loyalists = {
            pop_type = farmers
            value = 0.05
        }
    }
}

# 知识分子-虔信者 达尔文的阴影
bbg_common.20 = {
    type = country_event
    placement = root
    event_image = {
        video = "asia_union_leader"
    }
    title = bbg_common.20.t
    desc = bbg_common.20.d
    flavor = bbg_common.20.f
    trigger = {
        has_technology_researched = empiricism
        OR = {
            has_law = law_type:law_public_schools
            has_law = law_type:law_private_schools
        }
        ig:ig_intelligentsia = {
            is_powerful = yes
        }
        ig:ig_devout = {
            is_marginal = no
        }
        NOT = {
            has_global_variable = bbg_common.20.cooldown
        }
    }
    duration = 3
    
    option = {
        name = bbg_common.20.a
        set_global_variable = {
            name = bbg_common.20.cooldown
            days = 1095
        }
        ig:ig_intelligentsia = {
            add_modifier = {
                name = scientific_freedom_boost
                days = 1825
            }
        }
        ig:ig_devout = {
            add_modifier = {
                name = religious_authority_undermined
                days = 1825
            }
        }
        add_modifier = {
            name = secular_education_advancement
            days = 1825
        }
        add_radicals = {
            pop_type = clergymen
            value = 0.15
        }
    }
    
    option = {
        name = bbg_common.20.b
        set_global_variable = {
            name = bbg_common.20.cooldown
            days = 1095
        }
        ig:ig_devout = {
            add_modifier = {
                name = religious_education_protected
                days = 1825
            }
        }
        ig:ig_intelligentsia = {
            add_modifier = {
                name = academic_freedom_restricted
                days = 1825
            }
        }
        add_modifier = {
            name = traditional_values_reinforced
            days = 1825
        }
        add_radicals = {
            pop_type = academics
            value = 0.12
        }
    }
    
    option = {
        name = bbg_common.20.c
        add_treasury = -3000
        set_global_variable = {
            name = bbg_common.20.cooldown
            days = 1095
        }
        ig:ig_intelligentsia = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 1
            }
        }
        ig:ig_devout = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 1
            }
        }
        add_modifier = {
            name = intellectual_religious_dialogue
            days = 1825
        }
    }
}

# 实业家-工会 机器的崛起
bbg_common.21 = {
    type = country_event
    placement = root
    event_image = {
        video = "asia_union_leader"
    }
    title = bbg_common.21.t
    desc = bbg_common.21.d
    flavor = bbg_common.21.f
    trigger = {
        has_technology_researched = mechanical_production
        any_scope_building = {
            is_building_type = building_textile_mills
            level >= 5
        }
        ig:ig_industrialists = {
            is_powerful = yes
        }
        ig:ig_trade_unions = {
            is_marginal = no
        }
        NOT = {
            has_global_variable = bbg_common.21.cooldown
        }
    }
    duration = 3
    
    option = {
        name = bbg_common.21.a
        set_global_variable = {
            name = bbg_common.21.cooldown
            days = 1460
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = modernization_support
                days = 1825
            }
        }
        ig:ig_trade_unions = {
            add_modifier = {
                name = automation_displacement
                days = 1825
            }
        }
        add_modifier = {
            name = industrial_efficiency_boost
            days = 1825
        }
        add_radicals = {
            pop_type = machinists
            value = 0.08
        }
        add_radicals = {
            pop_type = laborers
            value = 0.12
        }
    }
    
    option = {
        name = bbg_common.21.b
        set_global_variable = {
            name = bbg_common.21.cooldown
            days = 1460
        }
        ig:ig_trade_unions = {
            add_modifier = {
                name = worker_protection_secured
                days = 1825
            }
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = innovation_restricted
                days = 1825
            }
        }
        add_modifier = {
            name = technological_conservatism
            days = 1825
        }
    }
    
    option = {
        name = bbg_common.21.c
        add_treasury = -5000
        set_global_variable = {
            name = bbg_common.21.cooldown
            days = 1460
        }
        add_modifier = {
            name = worker_retraining_program
            days = 1825
        }
        ig:ig_trade_unions = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 2
            }
        }
        ig:ig_industrialists = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 1
            }
        }
    }
}

# 地主-小市民 城市的边界
bbg_common.22 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.22.t
    desc = bbg_common.22.d
    flavor = bbg_common.22.f
    trigger = {
        has_technology_researched = urban_planning
        any_scope_state = {
            state_urbanization >= 15
            has_building = building_rye_farm
            state_building_levels = {
                building = building_rye_farm
                level >= 3
            }
        }
        ig:ig_landowners = {
            is_marginal = no
        }
        ig:ig_petty_bourgeoisie = {
            is_powerful = yes
        }
        NOT = {
            has_global_variable = bbg_common.22.cooldown
        }
    }
    duration = 3
    
    immediate = {
        random_scope_state = {
            limit = {
                state_urbanization >= 15
                has_building = building_rye_farm
                state_building_levels = {
                    building = building_rye_farm
                    level >= 3
                }
            }
            save_scope_as = expansion_state
        }
    }
    
    option = {
        name = bbg_common.22.a
        set_global_variable = {
            name = bbg_common.22.cooldown
            days = 1095
        }
        scope:expansion_state = {
            add_modifier = {
                name = urban_expansion_boost
                days = 1825
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = commercial_opportunity_expansion
                days = 1825
            }
        }
        ig:ig_landowners = {
            add_modifier = {
                name = agricultural_land_loss
                days = 1825
            }
        }
        add_radicals = {
            pop_type = farmers
            value = 0.1
        }
    }
    
    option = {
        name = bbg_common.22.b
        set_global_variable = {
            name = bbg_common.22.cooldown
            days = 1095
        }
        ig:ig_landowners = {
            add_modifier = {
                name = agricultural_protection_secured
                days = 1825
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = commercial_expansion_blocked
                days = 1825
            }
        }
        add_modifier = {
            name = rural_preservation_policy
            days = 1825
        }
        add_radicals = {
            pop_type = shopkeepers
            value = 0.08
        }
    }
    
    option = {
        name = bbg_common.22.c
        add_treasury = -4000
        set_global_variable = {
            name = bbg_common.22.cooldown
            days = 1095
        }
        add_modifier = {
            name = balanced_development_plan
            days = 1825
        }
        ig:ig_landowners = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 1
            }
        }
        ig:ig_petty_bourgeoisie = {
            add_modifier = {
                name = bbg_ig_approval_add
                days = 1095
                multiplier = 1
            }
        }
    }
}

# 实业家-地主-农民 铁路征地风波
bbg_common.23 = {
    type = country_event
    placement = root
    event_image = {
        video = "southamerica_election"
    }
    title = bbg_common.23.t
    desc = bbg_common.23.d
    flavor = bbg_common.23.f
    trigger = {
        has_technology_researched = railways
        NOT = {
            has_global_variable = bbg_common.23.cooldown
        }
    }
    duration = 3

	option = {
		name = bbg_common.23.a  # "强制征收，国家补偿！"
		set_global_variable = {
			name = bbg_common.23.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = railway_development_boost
				days = 730
				multiplier = 3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -3
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		add_treasury = -500
	}

	option = {
		name = bbg_common.23.b  # "协商征收，平衡各方"
		set_global_variable = {
			name = bbg_common.23.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = railway_employment_opportunity
				days = 730
				multiplier = 2
			}
		}
		add_treasury = -1000
	}

	option = {
		name = bbg_common.23.c  # "改变路线，避开争议"
		set_global_variable = {
			name = bbg_common.23.cooldown
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = railway_construction_delay
				days = 365
				multiplier = -2
			}
		}
	}
}

# 知识分子-虔信者-小市民 女性参政权运动
bbg_common.24 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.24.t
	desc = bbg_common.24.d
	flavor = bbg_common.24.f
	trigger = {
		has_technology_researched = empiricism
		OR = {
			has_law = law_type:law_public_schools
			has_law = law_type:law_private_schools
		}
		NOT = {
			has_global_variable = bbg_common.24.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.24.a  # "支持女性参政权！"
		set_global_variable = {
			name = bbg_common.24.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.24.b  # "有限支持地方选举权"
		set_global_variable = {
			name = bbg_common.24.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
	}

	option = {
		name = bbg_common.24.c  # "维护传统价值观"
		set_global_variable = {
			name = bbg_common.24.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -4
			}
		}
	}
}

# 小市民-实业家-工会 移民潮与排外情绪
bbg_common.25 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.25.t
	desc = bbg_common.25.d
	flavor = bbg_common.25.f
	trigger = {
		has_technology_researched = railways
		NOT = {
			has_global_variable = bbg_common.25.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.25.a  # "限制移民，保护本土"
		set_global_variable = {
			name = bbg_common.25.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.25.b  # "欢迎移民，促进发展"
		set_global_variable = {
			name = bbg_common.25.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.25.c  # "加强移民管理"
		set_global_variable = {
			name = bbg_common.25.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -300
	}
}

# 实业家-小市民-地主 银行危机与金融恐慌
bbg_common.26 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.26.t
	desc = bbg_common.26.d
	flavor = bbg_common.26.f
	trigger = {
		has_technology_researched = banking
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.26.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.26.a  # "政府救助银行"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = banking_crisis_government_bailout
			days = 1095
		}
		add_treasury = -2000
	}

	option = {
		name = bbg_common.26.b  # "让市场自我调节"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = financial_crisis_impact
				days = 1095
				multiplier = -2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = financial_crisis_impact
				days = 1095
				multiplier = -3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = financial_crisis_impact
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = banking_crisis_market_regulation
			days = 730
		}
	}

	option = {
		name = bbg_common.26.c  # "临时国有化银行"
		set_global_variable = {
			name = bbg_common.26.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = banking_crisis_nationalization
			days = 1095
		}
		add_treasury = -1000
	}
}

# 实业家-小市民-军队 殖民地贸易垄断争议
bbg_common.27 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.27.t
	desc = bbg_common.27.d
	flavor = bbg_common.27.f
	trigger = {
		has_technology_researched = navigation
		any_scope_state = {
			OR = {
				has_building = building_port
				has_building = building_trade_center
			}
		}
		NOT = {
			has_global_variable = bbg_common.27.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.27.a  # "维护贸易垄断"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = trade_monopoly_benefit
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
	}

	option = {
		name = bbg_common.27.b  # "开放自由贸易"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = colonial_instability_risk
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.27.c  # "军事管制贸易"
		set_global_variable = {
			name = bbg_common.27.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_treasury = -500
	}
}

# 地主-实业家-农民 货币制度改革辩论
bbg_common.28 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.28.t
	desc = bbg_common.28.d
	flavor = bbg_common.28.f
	trigger = {
		has_technology_researched = currency_standards
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.28.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.28.a  # "采用金本位"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = gold_standard_benefit
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.28.b  # "维持银本位"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.28.c  # "混合货币制度"
		set_global_variable = {
			name = bbg_common.28.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -800
	}
}

# 政府-各利益集团 国际债务危机
bbg_common.29 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.29.t
	desc = bbg_common.29.d
	flavor = bbg_common.29.f
	trigger = {
		has_technology_researched = banking
		treasury < 0
		NOT = {
			has_global_variable = bbg_common.29.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.29.a  # "增税偿还债务"
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -3
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_treasury = 3000
	}

	option = {
		name = bbg_common.29.b  # "削减政府支出"
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_treasury = 2000
	}

	option = {
		name = bbg_common.29.c  # "重新谈判债务"
		set_global_variable = {
			name = bbg_common.29.cooldown
			value = 0
			days = 365
		}
		random_list = {
			40 = {
				add_treasury = 1000
			}
			60 = {
				add_modifier = {
					name = diplomatic_crisis_modifier
					days = 730
				}
			}
		}
	}
}

# 知识分子-实业家-军队 外国投资与主权争议
bbg_common.30 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.30.t
	desc = bbg_common.30.d
	flavor = bbg_common.30.f
	trigger = {
		has_technology_researched = joint_stock_companies
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.30.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.30.a  # "限制外国投资"
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
	}

	option = {
		name = bbg_common.30.b  # "全面开放投资"
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = foreign_investment_boost
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.30.c  # "设立投资监管"
		set_global_variable = {
			name = bbg_common.30.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -600
	}
}

# 知识分子-虔信者-小市民 文化交流与民族认同
bbg_common.31 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.31.t
	desc = bbg_common.31.d
	flavor = bbg_common.31.f
	trigger = {
		has_technology_researched = empiricism
		NOT = {
			has_global_variable = bbg_common.31.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.31.a  # "促进文化交流"
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = cultural_division_modifier
				days = 365
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.31.b  # "保护民族文化"
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
	}

	option = {
		name = bbg_common.31.c  # "建立文化过滤"
		set_global_variable = {
			name = bbg_common.31.cooldown
			value = 0
			days = 365
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_treasury = -400
	}
}

# 军队-小市民-实业家 边境贸易与走私问题
bbg_common.32 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.32.t
	desc = bbg_common.32.d
	flavor = bbg_common.32.f
	trigger = {
		has_technology_researched = navigation
		any_scope_state = {
			has_building = building_port
		}
		NOT = {
			has_global_variable = bbg_common.32.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.32.a  # "军事打击走私"
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_treasury = -800
	}

	option = {
		name = bbg_common.32.b  # "降低关税减少走私"
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -1000
	}

	option = {
		name = bbg_common.32.c  # "腐败默许走私"
		set_global_variable = {
			name = bbg_common.32.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_modifier = {
			name = corruption_increase_modifier
			days = 1095
		}
	}
}

# 外交官-各利益集团 国际条约的国内分歧
bbg_common.33 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.33.t
	desc = bbg_common.33.d
	flavor = bbg_common.33.f
	trigger = {
		has_technology_researched = international_relations
		NOT = {
			has_global_variable = bbg_common.33.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.33.a  # "全面履行条约"
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = diplomatic_reputation_boost
			days = 1095
		}
	}

	option = {
		name = bbg_common.33.b  # "选择性执行条约"
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = diplomatic_tension_modifier
			days = 730
		}
	}

	option = {
		name = bbg_common.33.c  # "退出条约"
		set_global_variable = {
			name = bbg_common.33.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_modifier = {
			name = diplomatic_isolation_modifier
			days = 1095
		}
	}
}

# 小市民-知识分子-地主 新兴中产阶级的政治诉求
bbg_common.34 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.34.t
	desc = bbg_common.34.d
	flavor = bbg_common.34.f
	trigger = {
		has_technology_researched = empiricism
		NOT = {
			has_global_variable = bbg_common.34.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.34.a  # "扩大选举权"
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
	}

	option = {
		name = bbg_common.34.b  # "维持现状"
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
	}

	option = {
		name = bbg_common.34.c  # "渐进式改革"
		set_global_variable = {
			name = bbg_common.34.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
	}
}

# 军队-知识分子-虔信者 军工利益与和平主义
bbg_common.35 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.35.t
	desc = bbg_common.35.d
	flavor = bbg_common.35.f
	trigger = {
		has_technology_researched = gunsmithing
		any_scope_state = {
			has_building = building_arms_industry
		}
		NOT = {
			has_global_variable = bbg_common.35.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.35.a  # "削减军费支持和平"
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_treasury = 1500
	}

	option = {
		name = bbg_common.35.b  # "维持军工发展"
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = military_industrial_boost
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_treasury = -1000
	}

	option = {
		name = bbg_common.35.c  # "军转民用"
		set_global_variable = {
			name = bbg_common.35.cooldown
			value = 0
			days = 365
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_treasury = -800
	}
}

# 农民-小市民-实业家 城乡发展不平衡危机
bbg_common.36 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.36.t
	desc = bbg_common.36.d
	flavor = bbg_common.36.f
	trigger = {
		has_technology_researched = urbanization
		any_scope_state = {
			has_building = building_urban_center
		}
		NOT = {
			has_global_variable = bbg_common.36.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.36.a  # "农村发展计划"
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = rural_development_impact
				days = 730
				multiplier = -1
			}
		}
		add_treasury = -2500
	}

	option = {
		name = bbg_common.36.b  # "自由人口流动"
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = labor_supply_boost
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.36.c  # "城市优先发展"
		set_global_variable = {
			name = bbg_common.36.cooldown
			value = 0
			days = 365
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_treasury = -1500
	}
}

# 实业家内部分化-小市民 跨国公司与本土企业
bbg_common.37 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.37.t
	desc = bbg_common.37.d
	flavor = bbg_common.37.f
	trigger = {
		has_technology_researched = corporate_charters
		any_scope_state = {
			OR = {
				has_building = building_financial_district
				has_building = building_trade_center
			}
		}
		NOT = {
			has_global_variable = bbg_common.37.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.37.a  # "保护本土企业"
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = domestic_industry_protection
				days = 1095
				multiplier = 2
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		add_modifier = {
			name = trade_restriction_modifier
			days = 730
		}
	}

	option = {
		name = bbg_common.37.b  # "自由市场竞争"
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = market_competition_pressure
				days = 730
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = economic_efficiency_boost
			days = 1095
		}
	}

	option = {
		name = bbg_common.37.c  # "促进合资合作"
		set_global_variable = {
			name = bbg_common.37.cooldown
			value = 0
			days = 365
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = international_cooperation_benefit
				days = 1095
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -1200
	}
}

# 新增事件38: 蒸汽机时代的劳工抗议
bbg_common.38 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.38.t
	desc = bbg_common.38.d
	flavor = bbg_common.38.f
	trigger = {
		has_technology_researched = steam_engine
		any_scope_building = {
			is_building_type = building_textile_mills
			level >= 5
		}
		ig:ig_trade_unions = {
			is_marginal = no
		}
		NOT = {
			has_global_variable = bbg_common.38.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.38.a  # "镇压抗议，维护秩序"
		set_global_variable = {
			name = bbg_common.38.cooldown
			days = 1095
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_radicals = {
			pop_type = laborers
			value = 0.15
		}
	}

	option = {
		name = bbg_common.38.b  # "与工人谈判，寻求妥协"
		set_global_variable = {
			name = bbg_common.38.cooldown
			days = 1095
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		add_modifier = {
			name = labor_negotiation_modifier
			days = 730
		}
		add_treasury = -2000
	}

	option = {
		name = bbg_common.38.c  # "改善工作条件"
		set_global_variable = {
			name = bbg_common.38.cooldown
			days = 1095
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = improved_working_conditions
			days = 1460
		}
		add_treasury = -4000
	}
}

# 新增事件39: 铁路建设的社会冲击
bbg_common.39 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.39.t
	desc = bbg_common.39.d
	flavor = bbg_common.39.f
	trigger = {
		has_technology_researched = railways
		gdp >= 50000000
		any_scope_state = {
			infrastructure < 15
			market_access >= 0.7
		}
		NOT = {
			has_global_variable = bbg_common.39.cooldown
		}
	}
	duration = 3

	immediate = {
		random_scope_state = {
			limit = {
				infrastructure < 15
				market_access >= 0.7
			}
			save_scope_as = railway_target_state
		}
	}

	option = {
		name = bbg_common.39.a  # "国家主导建设"
		set_global_variable = {
			name = bbg_common.39.cooldown
			days = 1095
		}
		add_modifier = {
			name = railway_construction_boom
			days = 1460
		}
		scope:railway_target_state = {
			add_modifier = {
				name = rapid_railway_development
				days = 1825
			}
		}
		add_treasury = -100000
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
	}

	option = {
		name = bbg_common.39.b  # "私人投资建设"
		set_global_variable = {
			name = bbg_common.39.cooldown
			days = 1095
		}
		add_modifier = {
			name = private_railway_investment
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = railway_profits
				days = 1460
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
	}

	option = {
		name = bbg_common.39.c  # "缓慢推进，减少冲击"
		set_global_variable = {
			name = bbg_common.39.cooldown
			days = 1095
		}
		add_modifier = {
			name = gradual_railway_development
			days = 1825
		}
		add_treasury = -50000
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
	}
}

# 新增事件40: 政治改革的呼声
bbg_common.40 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.40.t
	desc = bbg_common.40.d
	flavor = bbg_common.40.f
	trigger = {
		has_law = law_type:law_autocracy
		OR = {
			has_law = law_type:law_oligarchy
			has_law = law_type:law_landed_voting
		}
		NOT = {
			has_global_variable = bbg_common.40.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.40.a  
		set_global_variable = {
			name = bbg_common.40.cooldown
			days = 1095
		}
		add_enactment_modifier = {
			name = reform_momentum
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		add_radicals = {
			value = -0.1
			strata = middle
		}
		add_radicals = {
			value = 0.05
			strata = upper
		}
	}

	option = {
		name = bbg_common.40.b  # "镇压改革呼声"
		set_global_variable = {
			name = bbg_common.40.cooldown
			days = 1095
		}
		add_modifier = {
			name = political_repression
			days = 1095
		}
		add_radicals = {
			value = 0.15
			strata = middle
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
	}

	option = {
		name = bbg_common.40.c  # "有限让步"
		set_global_variable = {
			name = bbg_common.40.cooldown
			days = 1095
		}
		add_modifier = {
			name = limited_concessions
			days = 730
		}
		add_loyalists = {
			value = 0.08
			strata = middle
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
	}
}

# 新增事件41: 科学与宗教的冲突
bbg_common.41 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.41.t
	desc = bbg_common.41.d
	flavor = bbg_common.41.f
	trigger = {
		has_technology_researched = empiricism
		OR = {
			has_law = law_public_schools
			has_law = law_private_schools
		}
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		ig:ig_devout = {
			is_marginal = no
		}
		NOT = {
			has_global_variable = bbg_common.41.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.41.a  # "支持科学教育"
		set_global_variable = {
			name = bbg_common.41.cooldown
			days = 1095
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1460
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1460
				multiplier = -4
			}
		}
		add_modifier = {
			name = scientific_education_boost
			days = 1825
		}
		add_radicals = {
			pop_type = clergymen
			value = 0.15
		}
	}

	option = {
		name = bbg_common.41.b  # "维护宗教传统"
		set_global_variable = {
			name = bbg_common.41.cooldown
			days = 1095
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1460
				multiplier = 4
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1460
				multiplier = -4
			}
		}
		add_modifier = {
			name = religious_education_emphasis
			days = 1825
		}
		add_radicals = {
			pop_type = academics
			value = 0.12
		}
	}

	option = {
		name = bbg_common.41.c  # "寻求平衡"
		set_global_variable = {
			name = bbg_common.41.cooldown
			days = 1095
		}
		add_treasury = -3000
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 1
			}
		}
		add_modifier = {
			name = balanced_education_approach
			days = 1460
		}
	}
}

# 新增事件42: 工业污染与环境争议
bbg_common.42 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.42.t
	desc = bbg_common.42.d
	flavor = bbg_common.42.f
	trigger = {
		has_technology_researched = mechanical_production
		any_scope_state = {
			OR = {
				has_building = building_textile_mills
				has_building = building_steel_mills
				has_building = building_chemical_plants
			}
			state_has_building_levels = 20
		}
		NOT = {
			has_global_variable = bbg_common.42.cooldown
		}
	}
	duration = 3

	immediate = {
		random_scope_state = {
			limit = {
				OR = {
					has_building = building_textile_mills
					has_building = building_steel_mills
					has_building = building_chemical_plants
				}
				state_has_building_levels = 20
			}
			save_scope_as = polluted_state
		}
	}

	option = {
		name = bbg_common.42.a  # "环保优先，限制工业"
		set_global_variable = {
			name = bbg_common.42.cooldown
			days = 1095
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		scope:polluted_state = {
			add_modifier = {
				name = environmental_protection_measures
				days = 1825
			}
		}
		add_treasury = -5000
	}

	option = {
		name = bbg_common.42.b  # "经济优先，继续发展"
		set_global_variable = {
			name = bbg_common.42.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 5
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -5
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		scope:polluted_state = {
			add_modifier = {
				name = industrial_expansion_unchecked
				days = 1825
			}
		}
		add_radicals = {
			value = 0.05
		}
	}

	option = {
		name = bbg_common.42.c  # "技术改进，减少污染"
		set_global_variable = {
			name = bbg_common.42.cooldown
			days = 1095
		}
		add_modifier = {
			name = clean_technology_investment
			days = 1825
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		add_treasury = -8000
	}
}

# 新增事件43: 货币制度改革争议
bbg_common.43 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.43.t
	desc = bbg_common.43.d
	flavor = bbg_common.43.f
	trigger = {
		has_technology_researched = currency_standards
		any_scope_state = {
			has_building = building_financial_district
		}
		NOT = {
			has_global_variable = bbg_common.43.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.43.a  # "采用金本位制"
		set_global_variable = {
			name = bbg_common.43.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = gold_standard_stability
				days = 1825
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = monetary_stability_boost
			days = 1825
		}
	}

	option = {
		name = bbg_common.43.b  # "维持银本位制"
		set_global_variable = {
			name = bbg_common.43.cooldown
			days = 1095
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = silver_standard_flexibility
			days = 1095
		}
	}

	option = {
		name = bbg_common.43.c  # "双金属本位制"
		set_global_variable = {
			name = bbg_common.43.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = bimetallic_standard_complexity
			days = 1460
		}
		add_treasury = -2000
	}
}

# 新增事件44: 殖民地贸易争端
bbg_common.44 = {
	type = country_event
	placement = root
	event_image = {
		video = "africa_diplomats_negotiating"
	}
	title = bbg_common.44.t
	desc = bbg_common.44.d
	flavor = bbg_common.44.f
	trigger = {
		has_technology_researched = navigation
		any_scope_state = {
			OR = {
				has_building = building_port
				has_building = building_trade_center
			}
		}
		NOT = {
			has_global_variable = bbg_common.44.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.44.a  # "维护贸易垄断"
		set_global_variable = {
			name = bbg_common.44.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = colonial_trade_monopoly
				days = 1460
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_modifier = {
			name = colonial_enforcement_costs
			days = 730
		}
	}

	option = {
		name = bbg_common.44.b  # "开放自由贸易"
		set_global_variable = {
			name = bbg_common.44.cooldown
			days = 1095
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = free_trade_benefits
			days = 1095
		}
	}

	option = {
		name = bbg_common.44.c  # "渐进开放政策"
		set_global_variable = {
			name = bbg_common.44.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = gradual_trade_liberalization
			days = 1095
		}
		add_treasury = -1500
	}
}

# 新增事件45: 军事现代化与传统冲突
bbg_common.45 = {
	type = country_event
	placement = root
	event_image = {
		video = "unspecific_airplane"
	}
	title = bbg_common.45.t
	desc = bbg_common.45.d
	flavor = bbg_common.45.f
	trigger = {
		has_technology_researched = military_statistics
		ig:ig_armed_forces = {
			is_marginal = no
		}
		any_scope_state = {
			has_building = building_barracks
		}
		NOT = {
			has_global_variable = bbg_common.45.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.45.a  # "全面军事现代化"
		set_global_variable = {
			name = bbg_common.45.cooldown
			days = 1095
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = military_modernization_support
				days = 1825
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		add_modifier = {
			name = modern_military_efficiency
			days = 1825
		}
		add_treasury = -10000
	}

	option = {
		name = bbg_common.45.b  # "保持传统军制"
		set_global_variable = {
			name = bbg_common.45.cooldown
			days = 1095
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		add_modifier = {
			name = traditional_military_values
			days = 1095
		}
	}

	option = {
		name = bbg_common.45.c  # "渐进式改革"
		set_global_variable = {
			name = bbg_common.45.cooldown
			days = 1095
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_landowners = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = gradual_military_reform
			days = 1460
		}
		add_treasury = -5000
	}
}

# 新增事件46: 城市化进程的社会问题
bbg_common.46 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.46.t
	desc = bbg_common.46.d
	flavor = bbg_common.46.f
	trigger = {
		has_technology_researched = urban_planning
		any_scope_state = {
			state_urbanization >= 20
		}
		NOT = {
			has_global_variable = bbg_common.46.cooldown
		}
	}
	duration = 3

	immediate = {
		random_scope_state = {
			limit = {
				state_urbanization >= 20
			}
			save_scope_as = urban_state
		}
	}

	option = {
		name = bbg_common.46.a  # "大规模城市改造"
		set_global_variable = {
			name = bbg_common.46.cooldown
			days = 1095
		}
		scope:urban_state = {
			add_modifier = {
				name = urban_renewal_project
				days = 1825
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_treasury = -15000
	}

	option = {
		name = bbg_common.46.b  # "自然发展，减少干预"
		set_global_variable = {
			name = bbg_common.46.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		scope:urban_state = {
			add_modifier = {
				name = urban_overcrowding_problems
				days = 1460
			}
		}
		add_radicals = {
			pop_type = laborers
			value = 0.1
		}
	}

	option = {
		name = bbg_common.46.c  # "分阶段改善"
		set_global_variable = {
			name = bbg_common.46.cooldown
			days = 1095
		}
		scope:urban_state = {
			add_modifier = {
				name = gradual_urban_improvement
				days = 2190
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_treasury = -7500
	}
}

# 新增事件47: 国际债务危机
bbg_common.47 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.47.t
	desc = bbg_common.47.d
	flavor = bbg_common.47.f
	trigger = {
		has_technology_researched = banking
		treasury < -50000
		NOT = {
			has_global_variable = bbg_common.47.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.47.a  # "增税偿还债务"
		set_global_variable = {
			name = bbg_common.47.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -4
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -2
			}
		}
		add_modifier = {
			name = debt_repayment_burden
			days = 1095
		}
		add_treasury = 75000
	}

	option = {
		name = bbg_common.47.b  # "削减政府支出"
		set_global_variable = {
			name = bbg_common.47.cooldown
			days = 1095
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		add_modifier = {
			name = government_austerity_measures
			days = 1460
		}
		add_treasury = 50000
	}

	option = {
		name = bbg_common.47.c  # "重新谈判债务条件"
		set_global_variable = {
			name = bbg_common.47.cooldown
			days = 1095
		}
		random_list = {
			40 = {
				add_treasury = 25000
				add_modifier = {
					name = successful_debt_negotiation
					days = 730
				}
			}
			60 = {
				add_modifier = {
					name = diplomatic_crisis_debt
					days = 1095
				}
			}
		}
	}
}

# 新增事件48: 女性参政权运动
bbg_common.48 = {
	type = country_event
	placement = root
	event_image = {
		video = "asia_union_leader"
	}
	title = bbg_common.48.t
	desc = bbg_common.48.d
	flavor = bbg_common.48.f
	trigger = {
		has_technology_researched = empiricism
		OR = {
			has_law = law_public_schools
			has_law = law_private_schools
		}
		ig:ig_intelligentsia = {
			is_powerful = yes
		}
		NOT = {
			has_global_variable = bbg_common.48.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.48.a  # "支持女性参政权"
		set_global_variable = {
			name = bbg_common.48.cooldown
			days = 1095
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1460
				multiplier = 4
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -1
			}
		}
		add_modifier = {
			name = womens_rights_advancement
			days = 1825
		}
	}

	option = {
		name = bbg_common.48.b  # "有限支持地方选举权"
		set_global_variable = {
			name = bbg_common.48.cooldown
			days = 1095
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = limited_womens_suffrage
			days = 1095
		}
	}

	option = {
		name = bbg_common.48.c  # "维护传统价值观"
		set_global_variable = {
			name = bbg_common.48.cooldown
			days = 1095
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_intelligentsia = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = -4
			}
		}
		add_modifier = {
			name = traditional_gender_roles
			days = 1460
		}
	}
}

# 新增事件49: 移民潮与社会融合
bbg_common.49 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.49.t
	desc = bbg_common.49.d
	flavor = bbg_common.49.f
	trigger = {
		has_technology_researched = railways
		gdp >= 75000000
		NOT = {
			has_global_variable = bbg_common.49.cooldown
		}
	}
	duration = 3

	option = {
		name = bbg_common.49.a  # "限制移民，保护本土"
		set_global_variable = {
			name = bbg_common.49.cooldown
			days = 1095
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_modifier = {
			name = immigration_restriction_policy
			days = 1460
		}
	}

	option = {
		name = bbg_common.49.b  # "欢迎移民，促进发展"
		set_global_variable = {
			name = bbg_common.49.cooldown
			days = 1095
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 3
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		ig:ig_trade_unions = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = -1
			}
		}
		add_modifier = {
			name = open_immigration_benefits
			days = 1460
		}
	}

	option = {
		name = bbg_common.49.c  # "选择性移民政策"
		set_global_variable = {
			name = bbg_common.49.cooldown
			days = 1095
		}
		add_treasury = -5000
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		ig:ig_industrialists = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 1
			}
		}
		add_modifier = {
			name = selective_immigration_system
			days = 1095
		}
	}
}

# 新增事件50: 自然灾害与社会救济
bbg_common.50 = {
	type = country_event
	placement = root
	event_image = {
		video = "southamerica_election"
	}
	title = bbg_common.50.t
	desc = bbg_common.50.d
	flavor = bbg_common.50.f
	trigger = {
		any_scope_state = {
			OR = {
				has_building = building_rye_farm
				has_building = building_wheat_farm
				has_building = building_rice_farm
			}
		}
		NOT = {
			has_global_variable = bbg_common.50.cooldown
		}
	}
	duration = 3

	immediate = {
		random_scope_state = {
			limit = {
				OR = {
					has_building = building_rye_farm
					has_building = building_wheat_farm
					has_building = building_rice_farm
				}
			}
			save_scope_as = disaster_state
		}
	}

	option = {
		name = bbg_common.50.a  # "政府全力救灾"
		set_global_variable = {
			name = bbg_common.50.cooldown
			days = 1095
		}
		scope:disaster_state = {
			add_modifier = {
				name = government_disaster_relief
				days = 730
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 1095
				multiplier = 4
			}
		}
		ig:ig_petty_bourgeoisie = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 365
				multiplier = 2
			}
		}
		add_treasury = -20000
	}

	option = {
		name = bbg_common.50.b  # "依靠民间慈善"
		set_global_variable = {
			name = bbg_common.50.cooldown
			days = 1095
		}
		ig:ig_devout = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 3
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = -2
			}
		}
		scope:disaster_state = {
			add_modifier = {
				name = private_charity_relief
				days = 1095
			}
		}
		add_radicals = {
			pop_type = farmers
			value = 0.1
		}
	}

	option = {
		name = bbg_common.50.c  # "军队协助救灾"
		set_global_variable = {
			name = bbg_common.50.cooldown
			days = 1095
		}
		ig:ig_armed_forces = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		ig:ig_rural_folk = {
			add_modifier = {
				name = bbg_ig_approval_add
				days = 730
				multiplier = 2
			}
		}
		scope:disaster_state = {
			add_modifier = {
				name = military_disaster_response
				days = 365
			}
		}
		add_treasury = -10000
	}
}

#TODO 事件链
#TODO 小市民 贪污
#TODO 农民-实业家 农思剧变

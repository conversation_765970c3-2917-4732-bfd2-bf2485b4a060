active_lens - 检查指定的镜头是否打开
active_lens_option - 检查指定的镜头选项是否已激活
add_to_temporary_list - 保存在触发器执行期间使用的临时目标
additional_war_exhaustion - 比较范围内的国家因目标外交博弈中的脚本事件而累积的额外战争损耗
scripted events in the target diplomatic play
age - 比较角色年龄
aggressive_diplomatic_plays_permitted - 如果国家独立或被允许发起自己的外交博弈，则为真
all_false - 如果所有子项都为假，则为真（等同于 NOR）
always - 检查分配的是/否值是否为真
and - 触发器内的所有条件都必须为真
any_active_law - 遍历一个国家的所有现行法律
any_active_party - 遍历一个国家的所有活跃政党
any_character - 遍历全球所有角色
any_character_in_exile_pool - 遍历流亡池中的角色
any_character_in_void - 遍历虚空中的角色
any_civil_war - 遍历与范围内国家相关的所有内战
any_cobelligerent_in_diplo_play - 遍历范围内国家在所有外交博弈中的所有共同交战方
(includes wars)
any_cobelligerent_in_war - 遍历范围内国家在所有战争中的所有共同交战方
any_combat_unit - 遍历输入范围内的所有作战单位
any_company - 遍历一个国家的所有公司
any_country - 遍历全球所有中央集权国家
any_decentralized_country - 遍历全球所有非中央集权国家
any_diplomatic_catalyst - 遍历一个国家近期记忆中的所有外交催化剂
any_diplomatic_play - 遍历全球所有外交博弈
any_diplomatically_relevant_country - 遍历一个国家范围内所有外交相关的国家
any_direct_subject - 当前层级中直接下属的任何国家
any_enemy_in_diplo_play - 遍历范围内国家在所有外交博弈中的所有敌人（包括
wars)
any_enemy_in_war - 遍历范围内国家在所有战争中的所有敌人
any_false - 如果任何子项为假，则为真（等同于 NAND）
any_harvest_condition - 遍历影响一个州、州区域、
strategic region, or country
any_in_global_list - 遍历全局列表中的所有项目。列表 = 名称或变量 = 名称
any_in_hierarchy - 当前层级中的任何国家，包括当前国家
any_in_list - 遍历列表中的所有项目。列表 = 名称或变量 = 名称
any_in_local_list - 遍历本地列表中的所有项目。列表 = 名称或变量 = 名称
any_influenced_interest_group - 遍历受政治运动影响的所有利益集团
any_interest_group - 遍历一个国家的所有利益集团
any_law - 遍历一个国家的所有法律
any_lobby_member - 遍历一个游说团体的所有利益集团成员
any_market - 遍历全球所有市场
any_market_goods - 遍历一个市场中所有活跃的（市场）商品
any_member - 遍历一个政党的所有利益集团成员
any_military_formation - 遍历当前输入范围内的所有军事编队
any_neighbouring_state - 遍历一个州区域的所有邻近州
any_overlord_or_above - 当前层级以上的任何国家
any_participant - 范围内外交协定的两个参与者中的任何一个
any_political_lobby - 遍历一个国家或利益集团中的所有政治游说团体
any_political_movement - 遍历一个国家的所有政治运动
any_potential_party - 遍历一个国家的所有潜在政党
any_power_bloc - 遍历所有权力集团
any_power_bloc_member - 遍历范围内权力集团的所有成员，包括领导者
any_preferred_law - 遍历一个利益集团国家中所有现行和可能的法律，
ordered by how much they prefer that law
any_primary_culture - 范围内国家或国家定义的主要文化
any_province - 遍历范围内州的所有省份
any_rival_country - 范围内国家正在与之竞争的任何国家
any_rivaling_country - 正在与范围内国家竞争的任何国家
any_scope_admiral - 遍历一个国家、利益集团或军事编队中的所有海军上将
any_scope_ally - 遍历一个国家的所有盟友
any_scope_building - 遍历一个州、国家中的所有建筑物
any_scope_character - 遍历一个国家、利益集团或战线中的所有角色
any_scope_country - 遍历在支持的范围内（当前：
market, strategic region)
any_scope_culture - 遍历范围内的所有文化
any_scope_diplomatic_pact - 范围内国家的任何外交协定
any_scope_front - 遍历与范围内战争相关的所有战线
any_scope_general - 遍历一个国家、利益集团、战线或军事编队中的所有将军
formation
any_scope_held_interest_marker - 遍历一个国家持有的所有利益标记
any_scope_initiator_ally - 遍历一个外交博弈中发起方的所有盟友
any_scope_interest_marker - 遍历一个国家、战略区域中的所有利益标记
any_scope_play_involved - 遍历一个外交博弈中的所有参与者
any_scope_politician - 遍历一个国家或利益集团中的所有政治家
any_scope_pop - 遍历一个国家、州、利益集团、文化中的所有人口
any_scope_state - 遍历一个国家、州区域、
theater, or front
any_scope_target_ally - 遍历一个外交博弈中目标的所有盟友
any_scope_theater - 遍历一个国家的所有战区
any_scope_violate_sovereignty_interested_parties - 遍历所有会对范围内国家主权被侵犯感兴趣的国家
their sovereignty violated
any_scope_violate_sovereignty_wars - 遍历如果目标国家主权被范围内国家侵犯时的所有相关战争
violated by scoped country
any_scope_war - 遍历与范围相关的所有战争
any_sea_node_adjacent_state - 遍历与一个州共享一个海洋节点的所有州
any_state - 遍历全球所有州
any_state_region - 遍历所有州区域
any_strategic_objective - 遍历范围内国家的所有战略目标州
any_subject_of_subject - 层级中直接附属国以下的任何国家
any_subject_or_below - 当前层级以下的任何国家
any_supporting_character - 遍历支持范围内政治运动的所有角色
any_trade_route - 遍历一个市场、国家、市场商品中的所有贸易路线
any_valid_mass_migration_culture - 列出范围内国家中适用于大规模迁移的文化
any_war_participant - 遍历一场战争中的所有参与者
appeasement - 比较一个政治游说团体的绥靖程度。
approaching_bureaucracy_shortage - 检查国家的机构是否会最终导致官僚机构短缺
eventually
arable_land - 检查州的可耕地
arable_land_country - 比较所有州的可耕地
army_mobilization_option_fraction - 检查一个国家的军队中拥有特定动员选项的单位达到一定百分比
monbilization option
army_power_projection - 与一个国家的总陆军力量投射进行比较
assert_if - 在运行时有条件地导致断言
assert_read - 在读取时有条件地导致断言
authority - 比较范围内国家的可用权威
available_jobs - 检查州内非自给自足建筑物的可用工作岗位数量
average_country_infrastructure - 检查范围内国家拥有的所有州的平均基础设施
average_incorporated_country_infrastructure - 检查范围内国家拥有的合并州的平均基础设施
average_sol_for_culture - 比较目标文化在该国的平均生活水平
average_sol_for_primary_cultures - 比较主要文化的平均生活水平
average_sol_for_religion - Compares the average standard of living for the target religion in the country
average_sol_for_slaves - 比较被奴役人口的平均生活水平
building_has_goods_shortage - 检查建筑物是否有任何投入品的短缺
bureaucracy - 比较范围内国家的可用官僚机构
calc_true_if - 如果指定数量的子触发器返回真，则返回真
can_activate_production_method - 检查范围内州特定类型的建筑物是否能够激活
the specified production method
can_add_wargoal_against - 检查范围内国家是否可以在活跃的外交博弈中对目标国家添加任何战争目标
diplomatic play
can_afford_diplomatic_action - 检查范围内国家是否能负担得起指定外交行动（协定或持续性）的影响力
can_agitate - 检查范围角色是否可以煽动目标国家
can_be_enacted - 检查一项法律是否可以由其国家根据其当前情况颁布
situation
can_break_diplomatic_pact - 检查与目标国家是否存在指定类型的外交协定
that can be broken by scope country
can_construct_building - 检查在范围内州是否可以建造 1 级 <建筑类型>
can_create_diplomatic_pact - 检查与另一个国家创建外交协定是否有效
can_decrease_autonomy - 检查一个附属国是否能够成为一个自治程度较低的附属国类型
can_establish_any_export_route - 检查一个国家是否可以建立任何出口路线
can_establish_any_import_route - 检查一个国家是否可以建立任何进口路线
can_establish_company - 检查该国是否可以建立一家新公司
can_form_nation - 检查目标国家是否有可能组建一个国家
can_have_as_subject - 检查一个国家是否可以将另一个国家作为特定类型的附属国
can_have_declared_interest_here - 检查目标国家范围是否满足在该战略区域拥有已宣布利益的条件规则。不检查已宣布利益的可用性。
declared interest in the strategic region in scope. Does not check for
availability of declared interests.
can_have_subjects - 检查该国是否能够拥有自己的附属国
can_increase_autonomy - 检查一个附属国是否能够成为一个更具自治性的附属国类型
can_invite_any_country - 检查是否可以邀请任何国家加入范围内的权力集团。
can_leave_power_bloc - 检查范围内国家是否可以离开其当前的权力集团（如果国家不在权力集团中则返回假）
if country is not in a Power Bloc)
can_lobbies_target - 检查目标国家是否是范围内国家游说团体的有效目标
can_own_autonomy_be_decreased - 检查范围内国家的自治权是否可以被降低
can_queue_building_levels - 检查建筑物的所有者是否可以排队等候提供的额外等级数量，而不会达到等级或资源潜力上限
levels without hitting a level or resource potential cap
can_research - 如果一个国家可以研究一项技术，则为真
can_send_diplomatic_action - 检查一个外交行动是否可以由范围内国家发送给目标国家
country
can_start_tutorial_lesson - 可以开始指定的教程课程吗？
can_take_on_scaled_debt - 检查范围内国家是否可以从另一个国家承担一定数量的按比例缩放的债务
another country
can_trigger_event - 检查国家是否可以触发指定的事件
cash_reserves_available - 评估一个生产建筑物的可用现金储备
cash_reserves_ratio - 评估一个生产建筑物的可用现金储备比率与其最大值相比
maximum
character_acceptance - 将范围内角色在目标国家的接受度与一个接受度值进行比较
acceptence value
character_supports_political_movement - 检查范围内角色是否支持一个政治运动
check_area - 比较对象与另一个对象的区域
civil_war_progress - 比较内战的进展
commander_is_available - 检查一个指挥官是否不忙
commander_rank - 比较指挥官的军衔
construction_queue_duration - 比较所有队列中完成建设所需的大致剩余周数的最大值：construction_queue_duration < 52
finish the constructions in any queue: construction_queue_duration < 52
construction_queue_government_duration - 比较私人队列中完成建设所需的大致剩余周数：construction_queue_private_duration < 52
constructions in the government queue: construction_queue_government_duration
< 52
construction_queue_num_queued_government_levels - 比较建设队列中政府建造的建筑等级数量
construction queue
construction_queue_num_queued_levels - 比较建设队列中的建筑等级数量
construction_queue_num_queued_private_levels - 比较建设队列中私人建造的建筑等级数量
queue
construction_queue_private_duration - 比较私人队列中完成建设所需的大致剩余周数：construction_queue_private_duration < 52
contains_capital_of - 检查范围内的州区域是否包含目标标签的首都
could_support_political_movement - 检查一个角色是否可能支持一个政治运动
country_army_unit_type_fraction - 检查一个国家拥有特定陆军单位类型的百分比
country_average_cultural_acceptance - 比较一个国家中存在的文化的平均接受度与一个值
against a value
country_average_religious_acceptance - 比较一个国家中存在的宗教的平均接受度与一个值
country_can_have_mass_migration_to - 检查范围内国家是否可以向目标国家进行大规模迁移
country_definition_has_culture - 检查一种文化是否是国家定义的文化之一
country_has_building_group_levels - 检查一个国家中一个建筑组的建筑等级总和
country_has_building_levels - 检查一个国家的建筑等级总和
country_has_building_type_levels - 检查一个国家中一个建筑类型的建筑等级总和
country_has_local_shortage - 范围内的市场商品在目标国家是否短缺
country_has_primary_culture - 检查一种文化是否是该国的主要文化之一
country_has_state_religion - 检查一种宗教是否是该国的国教
country_innovation - 检查一个国家每周产生的创新量
country_navy_unit_type_fraction - 检查一个国家拥有特定海军单位类型的百分比
country_or_subject_owns_entire_state_region - 检查范围内国家或其任何附属国是否拥有整个指定的州区域
specified state region
country_ownership_fraction - 比较一个国家拥有的该建筑等级的分数
country_rank - 比较一个国家的实力排名
country_tier - 比较国家标签的等级
country_turmoil - 比较国家的人口加权动荡
cultural_acceptance_base - 比较范围内国家中一种文化的共享文化特征的接受度与一个接受度值
cultural_acceptance_delta - 比较当前本地增量（修改）与范围内州一种文化的接受度
scoped state
culture_can_have_mass_migration_to_country - 检查范围内的文化是否可以向目标国家进行大规模迁移
culture_has_community_in_state - 检查范围内的文化在一个州是否有文化社区。
culture_percent_country - 检查一个国家的人口中拥有特定宗教的百分比
culture
culture_percent_state - 检查一个州的人口中拥有特定文化的百分比
culture_secession_progress - 检查一种文化在一个国家中走向分裂的进度百分比。如果该文化没有活跃的分裂运动，则为 0。
secession movement is active for the culture.
current_cohesion_number - 将当前的凝聚力作为一个数值进行比较
current_cohesion_percentage - 将当前的凝聚力作为范围内权力集团最大值的百分比进行比较
scope
current_manpower - 比较一个战斗方的当前人力
current_tooltip_depth - 返回屏幕上当前打开的工具提示数量
custom_description - 包装那些获得自定义描述而不是自动生成描述的触发器
custom_tooltip - 用自定义文本替换封闭触发器的工具提示
day_value - 日值
daynight_value - 昼夜值
debug_log - 记录父触发器是成功还是失败
debug_log_details - 记录父触发器是成功还是失败。记录哪些子触发器成功或失败
or failed
dependents - 比较范围内人口的受抚养人规模
devastation - 比较一个给定州的破坏程度
discriminates_religion - 检查范围内国家是否歧视给定的宗教（键）
earnings - 比较一个建筑物当前每位员工的年收入
economic_dependence - 比较范围内国家对目标国家的依赖程度。注意：这是一个昂贵的触发器
election_momentum - 将范围内政党的选举势头与一个值进行比较
empty_agitator_slots - 检查一个国家的空闲煽动者位置数量
enacting_any_law - 检查您是否正在制定任何法律。
enactment_chance - 比较范围内国家当前的制定机会（包括制定修正值）
enactment modifier)
enactment_chance_for_law - 比较范围内国家对给定法律的制定机会（包括制定修正值）
from enactment modifier)
enactment_chance_for_law_without_enactment_modifier - 比较范围内国家对给定法律的制定机会，但不包括制定修正值
enactment_chance_without_enactment_modifier - 比较范围内国家当前的制定机会，但不包括制定修正值
enactment_phase - 比较范围内国家当前的法律制定阶段。
enactment_setback_count - 比较范围内国家当前的制定挫折次数。
enemy_contested_wargoals - 确定战争中敌人当前正在争夺的战争目标的比例
contesting
enemy_occupation - 确定一个国家的（加权）敌人占领分数
escalation - 检查升级是否已超过某个阈值
exists - 检查指定范围目标是否存在（检查是否为 null 对象）
object)
expanding_institution - 检查机构是否正在扩张
experience_level - 比较角色经验等级
food_security - 检查一个人口是否有一定的粮食安全
formation_army_unit_type_fraction - 检查一个编队拥有特定陆军单位类型的百分比
formation_navy_unit_type_fraction - 检查一个编队拥有特定海军单位类型的百分比
fraction_of_levels_owned_by_country - 比较一个国家拥有的建筑物总等级的分数
free_arable_land - 检查州内的自由可耕地
free_principle_slots - 比较一个权力集团的自由原则槽位数量
game_date - 与当前游戏日期进行比较
gdp_ownership_ratio - 比较指定国家在范围内国家拥有的 GDP 比例
gdp_per_capita_ranking - 比较一个国家的人均 GDP 排名（位置）
global_country_ranking - 比较一个国家的实力排名（位置）
global_population - 比较全球人口
global_variable_list_size - 检查一个变量列表的大小
gold_reserves - 该国是否有必需的黄金储备
gold_reserves_limit - 比较该国的黄金储备上限
goods_production_rank - 与一个国家一种商品的生产排行榜排名进行比较
government_legitimacy - 比较合法性
government_transfer_of_power - 检查国家政府的权力转移
government_wage_level - 比较范围内国家的政府工资水平
government_wage_level_value - 比较范围内国家的政府工资水平值
harvest_condition_intensity - 在收获条件范围内，比较一个给定国家中收获条件的强度（因为州区域可以跨越多个国家）
in a given country (since the state region can span multiple countries)
has_active_building - 如果一个州有一个活跃的建筑类型，则为真
has_active_peace_deal - 如果该国正处于一场有拟议和平协议的战争中，则为真
has_active_production_method - 检查一个范围内的建筑物是否已激活指定的生产方法
has_any_secessionists_broken_out - 检查该国是否有分裂主义者爆发
has_any_secessionists_growing - 检查该国是否有任何分裂主义者在增长
has_assimilating_pops - 检查一个州是否有任何人口正在同化过程中。
has_attitude - 检查范围内国家对另一个国家是否有特定态度
has_battle_condition - 如果战斗方当前具有给定键的条件，则为真
has_building - 如果一个州/市场/州区域/国家有一种建筑类型，则为真
has_character_ideology - 检查范围内的政治运动是否有角色意识形态
has_civil_war_from_movement_type - 检查一个国家是否正在发生由特定运动类型引发的内战
has_claim - 检查范围内国家是否对州/州区域/国家有宣称权
has_claim_by - 检查一个州是否被一个国家宣称
has_commander_order - 检查范围内的角色是否正在遵循给定的命令
has_company - 检查范围内国家是否存在指定类型的公司
has_completed_subgoal - 检查范围内国家是否已完成某个子目标
has_consumption_tax - 检查该国是否正在对目标商品征税。
has_converting_pops - 检查一个州是否有任何人口正在转变过程中。
has_convoys_being_sunk - 检查该国是否有护航队因护航队突袭而被击沉
has_core_ideology - 检查范围内的政治运动是否有核心意识形态
has_cosmetic_dlc - 客户端是否有这个装饰性 DLC
has_cultural_community - 检查一种文化在范围内的州是否有文化社区。
has_cultural_obsession - 检查一种文化是否对某种商品有痴迷
has_culture - 检查角色文化
has_culture_graphics - 检查一种文化是否有某种文化图形
has_decreasing_interests - 检查该国的利益水平是否在下降
has_decree - 检查范围州是否有特定类型的法令
has_deployed_units - 检查一个建筑物是否支持任何已部署到总部以外的单位
HQ
has_diplomatic_pact - 检查两个国家是否有某种类型的主动外交协定
has_diplomatic_play - 检查战略区域是否有外交博弈
has_diplomatic_relevance - 检查目标国家对范围国家是否具有外交相关性
has_diplomats_expelled - 检查范围内国家最近是否驱逐了事件目标的外交官
has_discrimination_trait - 检查范围内的文化或宗教是否具有给定的歧视特征
has_dlc_feature - 主机是否有启用此特定功能的 DLC
has_employee_slots_filled - 检查某种人口类型的雇员数量是高于还是低于建筑物当前可雇用的总量的给定百分比。例如，如果一个建筑物在其当前对店主的招聘能力中达到了 80%（以 0.8 表示）。
given percentage of the total amount the building can currently hire. I.e. if a
building is at 80% (given as 0.8) of its current hiring capacity for
Shopkeepers, for example.
has_export_priority_tariffs - 检查范围内国家是否对一种商品有进口优先关税
has_failed_hires - 检查一个建筑物上周是否招聘失败
has_famine - 检查州或国家是否有饥荒
has_free_government_reform - 检查该国是否有一次免费的（无激进分子的）政府改革
has_game_rule - 给定的游戏规则设置是否已启用？
has_game_started - 如果游戏已开始，则为真
has_gameplay_dlc - 主机是否有这个游戏性 DLC
has_global_highest_gdp - 检查范围内国家是否拥有最高的 GDP
has_global_highest_innovation - 检查范围内国家是否拥有最高的每周创新
has_global_variable - 检查当前范围是否已设置指定的变量
has_global_variable_list - 检查当前范围是否已设置指定的变量列表
has_government_clout - 该国政府是否拥有必要的总影响力
has_government_type - 该国的政府类型是否如指定
has_harvest_condition - 检查范围内的州区域是否有某种类型的收获条件
has_healthy_economy - 检查该国是否有健康的经济
has_high_attrition - 检查一个军事编队的损耗风险是否高于其类型的基本值
for their type
has_homeland - 检查范围内的文化在目标州或州区域是否有家园
has_identity - 检查范围内的权力集团是否有特定的中心身份
has_ideology - 检查范围内的角色或利益集团是否有意识形态
has_import_priority_tariffs - 检查范围内国家是否对一种商品有进口优先关税
has_inactive_journal_entry - 检查该国是否至少有一个指定类型的非活动日志条目
type
has_institution - 检查范围国家是否有特定类型的机构
has_insurrectionary_interest_groups - 检查该国是否有叛乱的利益集团
has_interest_marker_in_region - 如果范围国家在目标区域有利益标记，则为真
has_journal_entry - 检查该国是否至少有一个指定类型的活动日志条目
has_label - 检查范围对象是否具有指定的标签
has_law - 检查一个国家是否有一项特定的法律生效
has_law_imposition_rights - 检查范围内国家是否拥有要求另一个国家颁布某项法律的必要许可
enacts a certain law
has_local_variable - 检查当前范围是否已设置指定的变量
has_local_variable_list - 检查当前范围是否已设置指定的变量列表
has_map_interaction - 检查地图交互类型是否已激活
has_map_interaction_diplomatic_action - 检查我们当前的地图交互是否是特定的外交行动
has_map_interaction_export_goods - 检查指定的镜头选项是否是出口指定的商品
has_map_interaction_import_goods - 检查指定的镜头选项是否是进口指定的商品
has_military_formation - 检查角色是否有军事编队
has_mobilization_option - 检查一个编队是否有特定的动员选项
has_mobilizing_unit - 检查范围内州的任何建筑物是否维持任何正在动员的作战单位
currently mobilizing
has_modifier - 检查一个受支持的范围是否有一个特定的计时修正
has_no_priority_tariffs - 检查范围内国家是否对一种商品有未优先的关税
has_objective - 检查范围内国家是否有某种目标类型
has_ongoing_assimilation - 检查范围内人口是否有持续的文化同化
has_ongoing_conversion - 检查范围内人口是否有持续的宗教转变
has_overlapping_interests - 检查范围内国家是否与目标国家的任何利益有重叠的利益标记
has_party - 如果 IG 范围有政党，则为真
has_party_member - 检查目标利益集团是否是范围政党的成员
has_play_goal - 检查外交博弈是否有某种战争目标类型
has_pop_culture - 检查人口是否有特定文化
has_pop_religion - 检查人口是否有特定宗教
has_port - 检查州是否至少有一个港口
has_possible_decisions - 检查一个国家是否有任何可能的决定
has_potential_resource - 检查指定的建筑组是否在范围内的州被允许。用于检查一个州是否可能生产一种资源
check if a state can potentially produce a resource
has_potential_to_form_country - Check if the target country could ever be able to form a nation
has_power_struggle - 检查范围权力集团是否有权力斗争
has_principle - 检查范围内的权力集团是否有特定的原则
has_principle_group - 检查范围内的权力集团在指定的组中是否有原则
has_reached_end_date - 如果已达到结束日期（NDefines::NGame::END_DATE），则为真
has_religion - 检查角色宗教
has_religious_taboo - 检查一种宗教是否对某种商品有禁忌
has_researchable_technology - 检查该国是否还有任何可研究的技术。
has_revolution - 检查该国是否有革命起义
has_role - 检查角色是否具有指定的角色
has_ruling_interest_group - 该国政府是否包括指定的 IG
has_ruling_interest_group_count - 该国政府是否由指定数量的 IG 组成
has_secret_goal - 检查范围内国家对另一个国家是否有特定的秘密目标
has_social_class - 检查范围内人口是否属于特定的社会阶层
has_social_hierarchy - 检查范围内国家是否采用了特定的社会等级制度
has_state_in_state_region - 检查国家在州区域是否有州
has_state_religion - 检查人口是否有国教
has_state_trait - 检查范围州是否有某种特征
has_strategic_adjacency - 检查范围内国家是否与目标州/国家有战略邻接（直接/沿海/战争目标邻接）
adjacency) to target state/country
has_strategic_land_adjacency - 检查范围内国家是否与目标州/国家有战略邻接（仅限直接陆地边界或战争目标邻接）
wargoal adjacency only) to target state/country
has_strategy - 检查范围内国家是否有特定的人工智能策略
has_subject_relation_with - 检查范围内国家是事件目标的附属国还是宗主国
has_sufficient_construction_capacity_for_investment - 检查国家是否有足够的建设能力来花费其所有收到的投资池资金。
incoming investment pool funds.
has_technology_progress - 该国是否已达到一项技术的必要进度
has_technology_researched - 如果一个国家已经研究了一项技术，则为真
has_template - 检查角色是否由指定的模板创建
has_terrain - 检查省份是否具有指定的地形类型
has_trait - 检查角色是否有特定特征
has_treaty_port_in_country - 检查范围内国家在目标国家是否有通商口岸
has_truce_with - 检查一个国家是否与另一个目标国家有休战协议
has_unification_candidate - 检查国家标签是否至少有一个统一候选者
has_unit_type - 检查一个作战单位是否是指定类型
has_variable - 检查当前范围是否已设置指定的变量
has_variable_list - 检查当前范围是否已设置指定的变量列表
has_war_exhaustion - 检查范围内战争中目标国家的战争损耗
has_war_goal - 检查战争是否有某种战争目标类型
has_war_support - 检查范围内战争中目标国家的战争支持度
has_war_with - 检查范围内国家是否与事件目标处于战争状态
has_wasted_construction - 检查国家是否在浪费其生产的任何建设。
hidden_trigger - 不为内部内容生成工具提示的 AndTrigger
highest_secession_progress - 比较一个给定国家中任何分裂运动的最高分裂进度
ig_approval - 与范围内的利益集团支持率进行比较
ig_clout - 与范围内的利益集团影响力进行比较
ig_government_power_share - 与范围内的利益集团的政治力量除以总政府政治力量进行比较
government political strength
ig_state_pol_strength_share - 如果范围内的 IG 在州内有脚本化的政治力量，则为真
in_default - 检查该国目前是否处于违约状态
in_election_campaign - 检查该国是否处于竞选期间
income_transfer - 比较外交协定的基本收入转移
incorporation_progress - 检查州的合并进度
influence - 比较范围内国家的可用影响力
infrastructure - 比较一个给定州的基础设施价值
infrastructure_usage - 比较一个给定州的基础设施使用价值
initiator_is - 检查谁是外交博弈的发起者
institution_investment_level - 比较一个机构的投资水平
interest_group_population - 比较一个利益集团的人口数量
interest_group_population_percentage - 比较一个利益集团的人口百分比
interest_group_supports_political_movement - 检查范围内的利益集团是否支持一个政治运动
investment_pool - 该国在其投资池中是否有这笔钱
investment_pool_gross_income - 该国是否有这笔总收入（扣除开支前的收入）用于其投资池
its investment pool
investment_pool_net_income - 该国是否有这笔净收入（扣除开支后的收入）用于其投资池
investment pool
is_active - 检查一次海军入侵是否当前处于活动状态（已开始或正在进行）
is_adjacent_to_country - 检查范围内国家是否与目标国家相邻
is_adjacent_to_state - 检查范围内国家是否与目标州相邻
is_advancing_on_front - 检查一个指挥官是否正在前线推进
is_ai - 如果国家范围由人工智能控制，则为真
is_army - 检查一个军事编队是否是陆军
is_at_war - 检查该国是否处于战争状态
is_attacker_in_battle - 检查一个指挥官在战斗中是否是攻击方
is_banning_goods - 检查一个国家是否在禁止一种商品
is_being_bolstered - 检查范围内的运动是否正在被加强
is_being_suppressed - 检查范围内的运动是否正在被压制
is_buildable - 检查一个建筑物是否可建造 = 是（默认）
is_building_group - 如果范围是给定组的建筑物，则为真
is_building_type - 如果范围是给定类型的建筑物，则为真
is_building_type_expanded - 检查特定 CBuildingType 的 CProductionMethodsPanelEntry 是否已展开
expanded
is_busy - 检查角色是否忙碌
is_capital - 检查州是否是所有者的首都 =布尔值
is_character_alive - 检查范围内的角色是否还活着
is_civil_war_type - 检查范围内的内战是否是特定类型
is_coastal - 检查州是否与一个（非不可通行的）海域接壤
is_considered_adjacent_due_to_wargoals - 检查范围内国家是否因其在已承诺的外交博弈中拥有与目标国家相邻的战争目标而被视为与目标国家相邻
virtue of the scoped country having wargoals that are adjacent to the target
is_construction_paused - 检查一个州的建设是否已暂停。
is_consumed_by_government_buildings - 检查市场商品是否对运行官僚机器至关重要
is_consumed_by_military_buildings - 检查商品是否对运行战争机器至关重要
is_country_alive - 检查范围内国家是否存活，即它是否在地图上至少有一个州并且可以与之互动
map and can be interacted with
is_country_type - 检查国家类型
is_defender_in_battle - 检查一个指挥官在战斗中是否是防御方
is_diplomatic_action_type - 检查外交协定是否是某种行动类型
is_diplomatic_catalyst_type - 检查外交催化剂是否是某种催化剂类型
is_diplomatic_pact_in_danger - 检查外交协定是否有破裂的危险
is_diplomatic_play_ally_of - 检查范围内国家是否与事件目标一起参与外交博弈
is_diplomatic_play_committed_participant - 如果国家是任何外交博弈的坚定参与者，则为真
is_diplomatic_play_enemy_of - 检查范围内国家是否在外交博弈中与事件目标对抗
is_diplomatic_play_initiator - 如果国家是任何外交博弈的发起者，则为真
is_diplomatic_play_involved_with - 检查范围内国家是否与事件目标参与了相同的外交博弈
target
is_diplomatic_play_participant_with - 检查范围内国家是否是与事件目标在同一外交博弈中的坚定参与者
play as event target
is_diplomatic_play_target - 如果国家是任何外交博弈的目标，则为真
is_diplomatic_play_type - 检查外交博弈是否是某种类型
is_diplomatic_play_undecided_participant - 如果国家是任何外交博弈的未定参与者，则为真
is_direct_subject_of - 检查范围内国家是否是事件目标的直接附属国（而非附属国的附属国）
is_employed - 检查人口是否就业
is_enacting_law - 检查范围内国家是否正在制定特定类型的法律。
is_expanding_institution - 您正在扩张一个机构吗
is_female - 检查角色是否为女性
is_fleet - 检查一个军事编队是否是舰队
is_forced_pact - 检查一个外交协定是否因摇摆或义务等原因而具有强制期限
is_forced_to_join_plays - 检查范围内国家是否被迫加入目标国家的外交博弈
Plays
is_fully_mobilized - 检查军事编队是否已完全动员
is_game_paused - 检查游戏是否已暂停
is_gamestate_tutorial_active - 游戏状态教程是否已激活？请参阅教程课程链文档中的 save_progress_in_gamestate。
tutorial_lesson_chains documentation.
is_goal_complete - 检查日志条目的目标是否已达到
is_government_funded - 检查一个建筑物是否由政府资助
is_heir - 检查范围内的角色是否是继承人
is_historical - 检查角色是否是历史人物
is_home_country_for - 检查一个国家是否是目标国家的母国
is_homeland - 检查范围内的州区域是否是目标文化的家园
is_homeland_of_country_cultures - 检查州是否是目标国家任何主要文化的家园
is_immortal - 检查范围内的角色是否是不朽的
is_immune_to_revolutions - 检查该国是否已通过 set_immune_to_revolutions 设置为对革命免疫
set_immune_to_revolutions
is_in_battle - 检查一个指挥官是否正在战斗中
is_in_customs_union - 检查该国是否是关税同盟的一部分
is_in_customs_union_with - 检查该国是否与范围内国家处于关税同盟中
is_in_exile_pool - 检查范围内的角色是否在流亡池中
is_in_government - 如果 IG 范围在政府中，则为真
is_in_list - 检查一个目标是否在列表中
is_in_mild_starvation - 检查人口是否处于轻度饥饿状态
is_in_power_bloc - 检查该国是否在一个权力集团中
is_in_revolt - 检查一个州是否有任何机会分裂成一个革命或分裂的国家
is_in_same_power_bloc - 检查范围内国家是否与目标范围内国家在同一个权力集团中
is_in_severe_starvation - 检查人口是否处于严重饥饿状态
is_in_starvation - 检查人口是否处于饥饿状态（轻度或重度）
is_in_void - 检查角色是否在虚空中
is_in_war_together - 检查范围内国家是否与事件目标在同一边参战
is_incorporated - 检查州是否已合并 =布尔值
is_indirect_subject_of - 检查范围内国家是否是事件目标的间接附属国（附属国的附属国）
is_insurrectionary - 检查国家、运动或 IG 是否是叛乱的
is_interest_active - 利益标记是否已激活
is_interest_group_type - 检查利益集团是否是某种类型
is_isolated_from_market - 检查一个州是否与市场隔绝
is_junior_in_customs_union - 如果国家是关税同盟中的初级国家，则为真
is_land_theater - 检查一个战区是否是陆地战区
is_largest_state_in_region - 检查州是否是州区域中最大的 =布尔值
is_lens_open - 检查某个镜头是否打开，指定为镜头键。指定一个可选的命名选项卡以检查此选项卡是否打开。
named tab to check if this tab is open.
is_local_player - 如果国家范围是玩家，则为真
is_losing_power_rank - 检查该国是否正在实力排名下降的过程中
is_marginal - 如果 IG 范围是边缘的，则为真
is_market_reachable_for_trade - 检查范围国家是否可以出于贸易目的到达指定的市场范围
trade
is_mass_migration_origin - 检查范围内国家是否是大规模迁移的起源地
is_mass_migration_origin_of_culture - 检查范围内国家是否是特定文化大规模迁移的起源地
is_mass_migration_target - 大规模迁移的目标是州。
is_mass_migration_target_for_culture - 范围内的州是指定文化的大规模迁移目标。
is_member_of_any_lobby - 检查利益集团是否是任何游说团体的成员
is_member_of_lobby - 检查利益集团是否是某种游说团体类型的成员
is_member_of_party - 检查利益集团是否是目标政党的成员
is_mobilized - 检查军事编队是否已动员
is_monarch - 检查角色是否是一个有世袭继承权国家的君主
is_naval_invasion_stalled_due_to_orders - 检查一次海军入侵是否因错误的海军上将命令而停滞
is_objective_completed - 范围内国家的目标是否已完成？
is_on_front - 检查一个角色或军事编队是否已被分配到一个战线并已到达那里
arrived there
is_owed_obligation_by - 检查范围内国家是否被目标国家欠下义务
is_panel_open - 检查某个信息面板是否打开，指定为事件目标（目标）或字符串（panel_name）。指定一个可选的命名选项卡（tab_name）以检查此选项卡是否打开。
as a string (panel_name). Specify an optional named tab (tab_name) to check if
this tab is open.
is_party - 检查目标政党是否与范围政党相同。仅在完全相同的政党对象上有效，这意味着您不能跨国家进行比较。
party object, meaning you can't compare across countries.
is_party_type - 检查范围政党的类型是否是指定的类型
is_player - 如果国家范围是玩家，则为真
is_political_lobby_type - 检查政治游说团体是否是某种游说团体类型
is_political_movement_type - 检查一个政治运动是否是特定类型
is_pop_type - 检查人口是否是指定类型
is_popup_open - 检查指定的弹出面板是否打开
is_power_bloc_leader - 检查该国是否是权力集团的领导者
is_powerful - 如果 IG 范围有影响力，则为真
is_primary_culture_of - 检查文化是否是该国任何主要文化之一
is_production_method_active - 检查范围内州特定类型的建筑物是否已激活指定的生产方法
production method active
is_progressing - 检查日志条目是否正在进行中
is_province_land - 检查省份是否在陆地上
is_reasonable_law_for_petition - 检查一项法律是否被认为对于政府请愿颁布是合理的
is_researching_technology - 检查该国是否正在积极研究一项技术
is_researching_technology_category - 检查该国是否正在积极研究一个技术类别
is_revolutionary - 检查国家、运动或利益集团是否是革命的
is_rightclick_menu_open - 检查指定的右键菜单是否打开
is_ruler - 检查角色是否是一个国家的统治者/国家元首
is_same_interest_group_type - 检查利益集团是否与目标具有相同的 IG 类型
is_same_law_group_as - 检查范围法律类型是否与目标法律类型范围在同一组中
is_same_party_type - 检查政党是否与目标具有相同的政党类型
is_sea_adjacent - 检查州是否与海域（常规或不可通行）接壤
is_secessionist - 检查国家、运动或利益集团是否是分裂主义者
is_set - 检查指定范围目标是否已设置（包括为 null 对象）
is_slave_state - 检查一个州是否雇用或有潜力雇用奴隶。
is_split_state - 检查范围内的州是否是一个分裂的州。
is_state_region_land - 检查州区域是否在陆地上
is_state_religion - 检查宗教是否是一个国家的国教
is_strategic_objective - 检查范围内的州是否是一个国家的战略目标
is_strongest_ig_in_government - 检查范围内的利益集团是否在所有政府利益集团中拥有最大的影响力
groups in government
is_subject - 如果国家是附属国，则为真
is_subject_of - 检查范围内国家是否是事件目标的附属国（或附属国的附属国）
is_subject_type - 检查国家的附属国类型
is_subsidized - 检查一个建筑物是否正在被补贴
is_subsistence_building - 检查一个建筑物是否是自给自足的建筑物
is_supporting_unification_candidate - 检查范围国家是否正在支持特定国家形成的统一候选人
is_target_in_global_variable_list - 检查一个目标是否在变量列表中
is_target_in_local_variable_list - 检查一个目标是否在变量列表中
is_target_in_variable_list - 检查一个目标是否在变量列表中
is_target_of_wargoal - 检查州是否是涉及特定国家的战争中任何战争目标的目标
is_taxing_goods - 检查一个国家是否在对一种商品征税
is_template_used - 检查角色模板是否已被使用
is_trade_route_active - 检查范围内的贸易路线是否已激活
is_trade_route_productive - 检查范围内的贸易路线是否有效率
is_tradeable - 检查一种商品或市场商品是否可交易
is_treaty_port - 检查范围内的州是否是通商口岸
is_tutorial_active - 教程是否已激活？
is_tutorial_lesson_active - 这是当前的教程课程吗？
is_tutorial_lesson_chain_completed - 具有指定键的教程课程链是否已完成？
is_tutorial_lesson_completed - 具有指定名称的教程课程是否已完成？
is_tutorial_lesson_step_completed - 教程课程步骤是否已完成？
is_under_colonization - 检查州是否正在被殖民
is_under_construction - 检查建筑物是否正在建设中
is_unification_candidate - 检查范围国家是否是国家标签的统一候选者
is_violating_sovereignty_of - 检查范围内国家是否正在侵犯目标国家的主权
is_vulnerable_front - 范围内的战线在目标方没有任何营或将军，而敌人至少有一个将军。
side, and the enemy has at least one General.
is_war - 如果外交博弈已升级为战争，则为真
is_war_participant - 检查目标国家是否是一场战争的参与者
is_warleader - 检查国家在战争中是否是战争领导者
isolated_states - 比较孤立州的数量
journal_entry_age - 返回日志条目的存在时间（自激活以来），以天为单位
law_approved_by - 检查范围内的法律是否得到利益集团的批准
law_enactment_stance - 比较范围内角色、运动或利益集团对指定法律的制定与同一组中当前现行法律的立场
about enactment of the specified law compared to the current active law in the
same group
law_progressiveness_difference - 比较范围法律类型与目标法律类型的进步性，值越高表示差异越大
higher value means greater difference
law_stance - 比较范围内角色、运动或利益集团对指定法律的基本立场，忽略同一组中当前现行的法律
leading_producer_of - 检查国家是否生产某种商品最多
leads_customs_union - 检查是否有其他国家是该国关税同盟的一部分
levels_owned_by_country - 比较一个国家在一座建筑物中拥有的等级数量
leverage_advantage - 检查权力集团与目标国家之间的杠杆优势是否满足条件
condition
liberty_desire - 比较范围内国家自由渴望值的触发器。
liberty_desire_weekly_progress - 比较范围内国家每周自由渴望进度值的触发器。
list_size - Checks the size of a list
literacy_rate - Checks if a pop, state or country has a certain amount of literacy
lobby_clout - Compare to total clout of scoped lobby's interest groups
lobby_formation_reason - Check scope diplomatic catalyst or political lobby scope for lobby formation
reason
local_variable_list_size - 检查一个变量列表的大小
loyalist_fraction - Compares loyalist fraction in pops in state or country, all parameters except
value are optional
loyalty - 比较一个给定州的忠诚度，即忠诚者的比例
market_access - 检查范围内的州的市场准入
market_goods_buy_orders - 检查市场商品是否有指定数量的购买订单
market_goods_cheaper - 检查市场商品是否比基准价格便宜至少指定的百分比
price
market_goods_consumption - 检查市场商品是否有指定数量的总消费量
market_goods_delta - 检查市场是否有指定的商品增量（生产 + 进口）-（消费 + 出口）
(consumption + exports)
market_goods_exports - 检查市场商品是否有指定数量的出口
market_goods_has_goods_shortage - 检查市场商品在市场上是否有短缺
market_goods_imports - 检查市场商品是否有指定数量的进口
market_goods_pricier - 检查市场商品是否比基准价格贵至少指定的百分比
base price
market_goods_production - 检查市场商品是否有指定数量的总产量
market_goods_sell_orders - 检查市场商品是否有指定数量的卖出订单
market_goods_shortage_ratio - 比较一种市场商品在其市场上的短缺比率
market_has_goods_shortage - 检查市场是否有任何建筑投入品的短缺
market_number_goods_shortages - 检查一个市场在其任何建筑投入品上有多少短缺
market_number_goods_shortages_with - 检查一个市场在其任何建筑投入品上有多少短缺，再加上来自目标国家的短缺
ones from the target country
market_number_goods_shortages_without - 检查一个市场在其任何建筑投入品上有多少短缺，减去来自目标国家的短缺
max_law_enactment_setbacks - 与一个国家在法律失败前可以承受的最大法律制定挫折次数进行比较
max_num_companies - 范围内国家允许拥有的公司数量上限
max_num_declared_interests - 比较范围内国家已宣布利益的最大数量
max_organization - 比较范围内军事编队的有效最大组织度
military_wage_level - 比较范围内国家的军事工资水平
military_wage_level_value - 比较范围内国家的军事工资水平值
month - 与当前游戏日期月份进行比较（1 月：0，12 月：11）
most_powerful_strata - 比较一个利益集团最强大的阶层
most_prominent_revolting_interest_group - 检查范围内州最突出的叛乱利益集团是否具有给定的利益集团类型。如果范围内的州没有叛乱，则评估为假。
revolt.
nand - 一个否定的与触发器
nationalization_cost - 比较将范围内国家所有由目标国家政府或人口拥有的建筑物国有化的总成本
naval_power_projection - 与一个国家的总海军力量投射进行比较
neighbors_any_power_bloc - 检查范围内国家是否与任何权力集团相邻。注意：范围内国家不被视为与其所属的权力集团（如有）相邻
is not considered to be neighboring the power bloc it belongs to (if any)
neighbors_member_of_same_power_bloc - 检查范围内国家是否与属于同一权力集团的任何其他国家相邻
power bloc
neighbors_power_bloc - 检查范围内国家是否与目标权力集团相邻。注意：范围内国家不被视为与其所属的权力集团（如有）相邻
any)
night_value - 夜值
nor - 一个否定的或触发器
not - 否定触发器的内容
num_casualties - 检查范围内战争中的总伤亡人数
num_companies - How many companies the scoped country has
num_country_casualties - 检查范围内战争中目标国家的伤亡人数
num_country_dead - 检查范围内战争中目标国家的死亡人数
num_country_wounded - 检查范围内战争中目标国家的受伤人数
num_cultural_communities - 检查州内的文化社区
num_dead - 检查范围内战争中的总死亡人数
num_declared_interests - 比较范围内国家已宣布利益的数量
num_diplomatic_pacts - 与一个国家指定类型的外交协定数量进行比较
num_investments_of_type - 检查范围内国家在其本国以外拥有的给定类型的建筑等级数量。
outside their own country.
num_mobilized_units_in_theater - 确定属于范围内战区所有者或其盟友的，在与范围内战区相交的战线上的动员单位数量
num_political_lobbies - 与范围内国家或利益集团的政治游说团体数量进行比较
num_power_bloc_members - 将一个权力集团的成员数量与一个值进行比较
num_power_bloc_states - 将一个权力集团的州数量与一个值进行比较
num_provinces_in_theater - 确定范围内战区的省份数量
num_subjects - 比较范围内国家的附属国数量
num_taxed_goods - 比较范围内国家的消费税商品数量
num_wounded - 检查范围内战争中的总受伤人数
number_of_claims - 该国对外国州的宣称权数量
number_of_possible_decisions - 一个国家可以做出的可能决定的数量
occupancy - 评估一个建筑物当前的占用率
organization - 比较范围内军事编队的组织度
overlord_can_decrease_subject_autonomy - 检查一个宗主国是否可以降低一个附属国的自治权
owes_obligation_to - 检查范围内国家是否欠事件目标一个义务
owns_entire_state_region - 检查国家是否拥有整个地区
owns_treaty_port_in - 国家是否拥有指定州区域的通商口岸
play_participant_has_war_goal_of_type_against - 检查范围国家是否在任何战争中持有针对特定国家的特定类型的战争目标
play_side_has_war_goal_of_type_against - 检查在任何外交博弈中与范围国家在同一方的任何国家是否持有针对特定国家的特定类型的战争目标
holds a wargoal of a specific type targeting a specificcountry
political_movement_identity_support - 比较具有正确文化/宗教身份的人口对政治运动的支持
political_movement_military_support - 比较政治运动的军事支持
political_movement_popular_support - 比较政治运动的民众支持
political_movement_radicalism - 比较政治运动的激进主义
political_movement_support - 比较政治运动的支持
political_movement_wealth_support - 比较政治运动的财富支持
politically_involved_ratio - 比较一个国家中政治参与人口的百分比
pollution_amount - 比较州区域污染
pollution_generation - 比较州内所有建筑物的总污染产生量
pop_acceptance - 将范围内人口的接受度与一个接受度值进行比较
pop_employment_building - 检查人口是否在特定建筑类型中工作
pop_employment_building_group - 检查人口是否在特定建筑类型中工作
pop_has_primary_culture - 检查人口的文化是否是主要的
pop_loyalist_fraction - 将一个人口中的激进分子数量与其总规模进行比较
pop_neutral_fraction - 将一个人口中的中立者（非激进分子，非忠诚者）数量与其总规模进行比较
total size
pop_radical_fraction - 将一个人口中的激进分子数量与其总规模进行比较
pop_type_percent_country - 检查范围内国家的人口中是否有 <百分比> 属于指定的民众类型
belonging to the specified pop type
pop_type_percent_state - 检查一个州的人口中拥有特定民众类型的百分比
population_by_culture - 比较范围内州目标文化的当前人口
potential_diplomatic_play_power_ratio - 检查范围内国家方与目标国家方在潜在外交博弈中的预期力量比
power_bloc_rank - 比较一个权力集团的排名
power_bloc_share_gdp - 比较范围内国家在其所有权力集团成员减去一个成员中的 GDP 份额，如果不在权力集团中则返回 -1
current members, returns -1 if not in a Power Bloc
power_bloc_share_gdp_with - 比较范围内国家在其所有权力集团成员减去一个成员中的 GDP 份额，如果不在权力集团中则返回 -1
members plus an additional country, returns -1 if not in a Power Bloc
power_bloc_share_gdp_without - 比较范围内国家在其所有权力集团成员减去一个成员中的 GDP 份额，如果不在权力集团中则返回 -1
power_bloc_share_power_projection - 比较范围内国家在其所有权力集团成员减去一个成员中的力量投射份额，如果不在权力集团中则返回 -1
Power Bloc members, returns -1 if not in a Power Bloc
power_bloc_share_power_projection_with - 比较范围内国家在其所有权力集团成员减去一个成员中的力量投射份额，如果不在权力集团中则返回 -1
power_bloc_share_power_projection_without - 比较范围内国家在其所有权力集团成员减去一个成员中的力量投射份额，如果不在权力集团中则返回 -1
power_bloc_share_prestige - 比较范围内国家在其所有权力集团成员减去一个成员中的威望份额，如果不在权力集团中则返回 -1
power_bloc_share_prestige_with - 比较范围内国家在其所有权力集团成员减去一个成员中的威望份额，如果不在权力集团中则返回 -1
power_bloc_share_prestige_without - 比较范围内国家在其所有权力集团成员减去一个成员中的威望份额，如果不在权力集团中则返回 -1
power_bloc_total_leading_goods_producers - 比较全球所有商品的主要生产商中有多少成员，按其在排名中的位置加权，减去一个目前在权力集团中的国家。最高生产商按 MIN_SPOT_PRESTIGE_AWARD 加权，然后每个后续位置减一加权，直到。默认情况下，这意味着第一名计为 3，第二名计为 2，第三名计为 1。任何其他位置计为 0。
weighted by their position in the ranking.The top producer is weighted by
MIN_SPOT_PRESTIGE_AWARD and then each subsequent position is weighted by one
less until.By default this means 1st counts as 3, 2nd as 2 and 3rd as 1. Any
other positions as 0.
power_bloc_total_leading_goods_producers_with - 比较全球所有商品的主要生产商中有多少成员，按其在排名中的位置加权，减去一个目前在权力集团中的国家。最高生产商按 MIN_SPOT_PRESTIGE_AWARD 加权，然后每个后续位置减一加权，直到。默认情况下，这意味着第一名计为 3，第二名计为 2，第三名计为 1。任何其他位置计为 0。
power_bloc_total_leading_goods_producers_without - 比较全球所有商品的主要生产商中有多少成员，按其在排名中的位置加权，减去一个目前在权力集团中的国家。最高生产商按 MIN_SPOT_PRESTIGE_AWARD 加权，然后每个后续位置减一加权，直到。默认情况下，这意味着第一名计为 3，第二名计为 2，第三名计为 1。任何其他位置计为 0。
then each subsequent position is weighted by one less until.By default this
means 1st counts as 3, 2nd as 2 and 3rd as 1. Any other positions as 0.
power_bloc_worst_economic_dependence - 如果在权力集团范围内使用，则比较对领导者经济依赖程度最低的成员。
dependence on the leader.
power_bloc_worst_economic_dependence_with - 如果在权力集团范围内使用，则比较对领导者经济依赖程度最低的成员。
power_bloc_worst_economic_dependence_without - 如果在权力集团范围内使用，则比较对领导者经济依赖程度最低的成员。
power_bloc_worst_infamy - 如果在权力集团范围内使用，则比较恶名值最差（最高）的成员国的恶名值
with the worst (highest) Infamy
power_bloc_worst_infamy_with - 如果在权力集团范围内使用，则比较恶名值最差（最高）的成员国的恶名值
power_bloc_worst_infamy_without - 如果在权力集团范围内使用，则比较恶名值最差（最高）的成员国的恶名值
power_bloc_worst_leader_relations - 如果在权力集团范围内使用，则比较与权力集团领导者关系最差（最低）的成员国与领导者的关系值
leader for the member country with the worst (lowest) Relations
power_bloc_worst_leader_relations_with - 如果在权力集团范围内使用，则比较与权力集团领导者关系最差（最低）的成员国与领导者的关系值
power_bloc_worst_leader_relations_without - 如果在权力集团范围内使用，则比较与权力集团领导者关系最差（最低）的成员国与领导者的关系值
power_bloc_worst_leader_religion_population_fraction - 如果在权力集团范围内使用，则比较一个成员国中追随领导者宗教的最低人口比例。
member country that follows the leader's religion.
power_bloc_worst_leader_religion_population_fraction_with - 如果在权力集团范围内使用，则比较一个成员国中追随领导者宗教的最低人口比例。
power_bloc_worst_leader_religion_population_fraction_without - 如果在权力集团范围内使用，则比较一个成员国中追随领导者宗教的最低人口比例。
power_bloc_worst_liberty_desire - 如果在权力集团范围内使用，则比较自由渴望值最差（最高）的成员国的自由渴望值
power_bloc_worst_liberty_desire_with - 如果在权力集团范围内使用，则比较自由渴望值最差（最高）的成员国的自由渴望值
power_bloc_worst_liberty_desire_without - 如果在权力集团范围内使用，则比较自由渴望值最差（最高）的成员国的自由渴望值
power_bloc_worst_progressiveness_difference_government_type - 如果在权力集团范围内使用，则比较其治理原则和权力分配法与权力集团领导者相比，进步性差异最差（最高）的成员国的总进步性值差异
difference for the member country with the worst (highest) difference in
progressiveness between their Governance Principles and Distribution of Power
Laws compared to the power bloc leader
power_bloc_worst_progressiveness_difference_government_type_with - 如果在权力集团范围内使用，则比较其治理原则和权力分配法与权力集团领导者相比，进步性差异最差（最高）的成员国的总进步性值差异
power_bloc_worst_progressiveness_difference_government_type_without - 如果在权力集团范围内使用，则比较其治理原则和权力分配法与权力集团领导者相比，进步性差异最差（最高）的成员国的总进步性值差异
predicted_cohesion_percentage_with - 预测一个权力集团（以 1 的分数表示）如果将目标国家作为成员加入后的凝聚力
prefers_law - 检查范围内的利益集团是否更喜欢指定的法律而不是比较法律
law
prestige - 比较威望
primary_cultures_percent_country - 检查一个国家的人口中拥有该国主要文化的百分比
primary cultures
primary_cultures_percent_state - 检查一个州的人口中拥有该国主要文化的百分比
private_ownership_fraction - 比较该建筑物私有等级的分数
produced_authority - 比较范围内国家产生的权威
produced_bureaucracy - 比较范围内国家产生的官僚机构
produced_influence - 比较范围内国家产生的影响力
quality_of_life - 比较给定人口的生活质量
radical_fraction - 比较州或国家人口中的激进分子比例，除值外的所有参数都是可选的
radical_population_fraction - 比较一个给定国家中激进分子占人口的比例
real_month - 与当前真实日期月份进行比较（1 月：1，12 月：12）
relative_infrastructure - 比较一个州的基础设施与基础设施使用情况
religion_percent_country - 检查一个国家的人口中拥有特定宗教的百分比
religion
religion_percent_state - 检查一个州的人口中拥有特定宗教的百分比
remaining_undepleted - 检查一个州剩余的资源量，例如金矿
ruler_can_have_command - 检查该国的政府类型是否允许其统治者拥有指挥权
save_temporary_scope_as - 保存在触发器执行期间使用的临时目标
save_temporary_scope_value_as - 将一个数值或布尔值保存为一个任意命名的临时目标，以便在同一效果中稍后引用
referenced later in the same effect
scaled_debt - 将价值与一个国家的债务相对于债务上限进行比较
scaled_gold_reserves - 将价值与一个国家的黄金储备相对于储备限额进行比较
scripted_bar_progress - 确定一个脚本化进度条的进度
self_ownership_fraction - 比较该建筑物拥有的等级中由建筑物本身拥有的分数
itself
shares_heritage_and_other_trait_with_any_primary_culture - 检查文化是否与该国任何主要文化共享任何标记为“遗产”的特征和其他“非遗产”特征
trait with any of a country's primary cultures
shares_heritage_trait_with_any_primary_culture - 检查文化是否与该国任何主要文化共享任何标记为“遗产”的特征
shares_heritage_trait_with_state_religion - 检查宗教是否与一个国家的宗教共享任何标记为“宗教团体”的特征
shares_non_heritage_trait_with_any_primary_culture - 检查文化是否与该国任何主要文化共享除标记为“遗产”之外的任何特征
shares_trait_with_any_primary_culture - 检查该文化是否与该国任何主要文化共享任何特征
shares_trait_with_state_religion - 检查宗教是否与一个国家的国教共享任何特征
should_set_wargoal - 检查该国是否在它参与的任何外交博弈中缺少一个主要战争目标
part of
should_show_nudity - 可以显示裸体吗？
shrinking_institution - 检查机构是否正在萎缩
size_weighted_lost_battles_fraction - 确定目标国家在目标战争中输掉的战斗比例，按战争中所有战斗的人力规模加权
war, weighted by manpower size of all battles in the war
sol_ranking - 比较一个国家的生活水平排名（位置）
standard_of_living - 比较一个给定人口的生活水平
starting_manpower - 比较一个战斗方的起始人力
state_cultural_acceptance - 检查目标文化的人口在范围内的州被接受的程度
state_goods_cheaper - 检查州商品是否比基准价格便宜至少指定的百分比
state_goods_consumption - 检查州商品是否有指定数量的总消费量
state_goods_delta - 检查州是否有指定的商品增量（生产 - 消费）
state_goods_has_local_goods_shortage - 检查州商品在一个州是否有短缺，但不是在整个市场上
state_goods_pricier - 检查州商品是否比基准价格贵至少指定的百分比
state_goods_production - 检查州商品是否有指定数量的总产量
state_has_building_group_levels - 检查一个州一个建筑组的建筑等级总和
state_has_building_levels - 检查一个州的建筑等级总和
state_has_building_type_levels - 检查一个州一个建筑类型的建筑等级总和
state_has_goods_shortage - 检查州是否有任何建筑投入品的短缺
state_is_eligible_as_mass_migration_target - 检查州是否可以接收大规模迁移。
state_population - 检查范围内州的总人口
state_religious_acceptance - 检查给定宗教的人口在范围内的州被接受的程度
state_unemployment_rate - 检查范围内州的失业率（百分比）
strata - 检查范围内人口的阶层
subject_can_increase_autonomy - 检查一个附属国是否可以增加其自治权
supply_network_strength - 比较该国的供应网络强度（可以超过 1）
switch - 打开一个触发器以评估另一个触发器，带有一个可选的回退触发器。
fallback trigger.
taking_loans - 检查该国目前是否每周出现赤字并正在借款以弥补
compensate
target_cohesion_number - 将目标凝聚力作为一个数值进行比较
target_cohesion_percentage - 将目标凝聚力作为范围内权力集团最大值的百分比进行比较
target_is - 检查谁是外交博弈的目标
tax_capacity - 检查范围内的州的税收能力
tax_capacity_usage - 检查范围内的州的税收能力使用情况
tax_level - 比较范围内国家的总体税收水平
tax_level_value - 比较范围内国家的总体税收水平整数值
tenure_in_current_power_bloc_days - 比较范围内国家成为其当前权力集团成员的天数
tenure_in_current_power_bloc_months - 比较范围内国家成为其当前权力集团成员的（整）月数
tenure_in_current_power_bloc_weeks - 比较范围内国家成为其当前权力集团成员的（整）周数
tenure_in_current_power_bloc_years - 比较范围内国家成为其当前权力集团成员的（整）年数
total_population - 比较一个给定国家的总人口
total_population_including_subjects - 比较范围内国家及其附属国的总人口
total_population_including_subjects_share - 比较范围内国家及其附属国占全球人口的份额
total_population_share - 比较一个给定国家占全球人口的份额
population
total_size - 比较范围内人口的总规模
total_urbanization - 比较一个给定州的总城市化水平/ntotal_urbanization > 5
total_used_principle_levels - 比较一个权力集团的活动原则等级总数
trade_route_needs_convoys_to_grow - 检查范围内的贸易路线是否需要更多护航队才能增长
trait_value - 比较角色的总特征值
trigger_else - 如果前面的 'trigger_if' 或 'trigger_else_if' 的 display_triggers 不满足，则评估触发器
'trigger_else_if' is not mettrigger_if = { limit = { <display_triggers> }
<triggers> }
trigger_else_if - 如果前面的 `trigger_if` 或 `trigger_else_if` 的 display_triggers 不满足并且其自己的 limit 的 display_trigger 满足，则评估封闭的触发器
`trigger_if` or `trigger_else_if` is not met and its own display_trigger of the
limit is mettrigger_if = { limit = { <display_triggers> } <triggers>
}
trigger_if - 如果 limit 的 display_triggers 满足，则评估触发器
turmoil - 比较一个给定州的动荡，即激进分子的比例
used_principle_slots - 比较一个权力集团已使用的原则槽位数量
variable_list_size - 检查一个变量列表的大小
war_exhaustion_from_acceptance_of_dead - 确定一个国家因其对战争中阵亡人力（无论在哪一方）的文化接受程度而产生的战争损耗
acceptance of manpower killed in the war, regardless of what side they were on
war_has_active_peace_deal - 如果战争有拟议的和平协议，则为真
war_participant_has_war_goal_of_type_against - 检查范围国家是否在任何战争中持有针对特定国家的特定类型的战争目标
war_side_has_war_goal_of_type_against - 检查在任何战争中与范围国家在同一方的任何国家是否持有针对特定国家的特定类型的战争目标
was_exiled - 检查范围内的角色是否被流放
was_formed_from - 检查一个形成的国家以前是否有一个特定的定义
wealth - 检查一个人口是否有一定的财富
weekly_net_fixed_income - 该国在扣除固定开支后是否有这笔每周收入
weekly_profit - 检查该建筑物本周的利润是高于还是低于一个给定值
weighted_calc_true_if - 如果满足的子触发器的权重总和达到指定总和，则返回真
workforce - Compares the workforce size of the scoped pop
would_accept_diplomatic_action - Checks if a country would accept a diplomatic action proposed by another country
year - Compares the current year of the game
years_of_service - 比较指挥官的服役年限
specific places
revolutions due to for example being a revolutionary country itself, only
is_repairing - 检查一个海军上将在输掉一场海战后是否不可用并正在修理
is_revolutionary_movement - 检查范围内的政治运动是否正在该国引发一场酝酿中的革命
is_traveling - 检查指挥官是否正在旅行
pop_is_discriminated - Checks if pop is discriminated against
religion_accepted - Checks if pop's religion is accepted
army_reserves - 比较陆军预备役的数量
battalion_manpower - 比较一个国家营的当前总人力
battle_side_pm_usage - 检查范围内战斗中，目标国家一方具有指定生产方法的作战单位的比例与该值的比较
commander_pm_usage - 检查范围内指挥官上，目标国家一方具有指定生产方法的作战单位的比例与该值的比较
< 52
country_pm_usage - 检查范围内国家（同类型）具有指定生产方法的作战单位的比例与该值的比较
only be used in specific places
diplomatic_play_pm_usage - 检查范围内外交博弈中，目标国家一方具有指定生产方法的作战单位（同类型）的比例与该值的比较
Play, on the target country's side, with the specified Production Method
compares to the value
flotilla_manpower - 比较一个国家小舰队的当前总人力
front_side_pm_usage - 检查范围内战线上，目标国家一方具有指定生产方法的作战单位的比例与该值的比较
(consumption + exports)
mobilization_cost - 比较该角色的当前动员成本
navy_reserves - 比较海军预备役的数量
total_manpower - 比较一个国家作战单位的当前总人力
AND - True if all the inner triggers are true. False otherwise.
OR - True if any of the inner triggers is true. False otherwise.
NOT - Negates content of trigger
NAND - A negated AND trigger. True if none of the inner triggers are true.
NOR - A negated OR trigger. True if all inner triggers are false.
`trigger_else_if` is not met and its own display_trigger of the limit is met.
'trigger_else_if' is not met
error_check - 检查专门使用 CErrorTable::CheckTrigger 路径的代码中的错误。通常避免使用此方法，而应使用普通脚本。
path.In general avoid this and just use normal script.

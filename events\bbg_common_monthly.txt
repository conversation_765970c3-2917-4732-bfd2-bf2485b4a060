namespace = bbg_common
# 每月检查事件
bbg_common.100 = {
	type = country_event
	hidden = yes
	immediate = {
        remove_global_variable = bbg_use_short_events
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common.1.over365
				}
				OR = {
					has_law = law_type:law_serfdom
					has_law = law_type:law_tenant_farmers
				}
			}
			random_list = {
				40 = {
					if = {
						limit = {
							has_law = law_type:law_serfdom
						}
						if = {
							limit = {
								has_global_variable = bbg_use_short_events
							}
							trigger_event = {
								id = bbg_common.1_short
							}
						}
						else = {
							trigger_event = {
								id = bbg_common.1
							}
						}
					}
				}
				30 = {
					if = {
						limit = {
							has_law = law_type:law_tenant_farmers
						}
						if = {
							limit = {
								has_global_variable = bbg_use_short_events
							}
							trigger_event = {
								id = bbg_common.1_short
							}
						}
						else = {
							trigger_event = {
								id = bbg_common.1
							}
						}
					}
				}
				30 = {
				}
				# 留出权重让事件不发生
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common.2.over365
				}
				OR = {
					has_law = law_type:law_landed_voting
					has_law = law_type:law_wealth_voting
					has_law = law_type:law_census_voting
					has_law = law_type:law_universal_suffrage
				}
				OR = {
					has_law = law_type:law_religious_schools
					has_law = law_type:law_private_schools
					has_law = law_type:law_public_schools
				}
				NOT = {
					has_law = law_type:law_slavery_banned
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.2_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.2
						}
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common.3.over365
				}
				OR = {
					ig:ig_industrialists = {
						is_powerful = yes
					}
					ig:ig_trade_unions = {
						is_powerful = yes
					}
				}
			}
			random_list = {
				50 = {
					# 每月有25%的概率触发
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.3_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.3
						}
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_4_cooldown
				}
				any_interest_group = {
					OR = {
						is_interest_group_type = ig_petty_bourgeoisie
						is_interest_group_type = ig_industrialists
					}
					is_powerful = yes
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.4_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.4
						}
					}
					set_global_variable = {
						name = bbg_common_4_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_5_cooldown
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.5_short
							days = 1
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.5
							days = 1
						}
					}
					set_global_variable = {
						name = bbg_common_5_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_6_cooldown
				}
				has_law = law_type:law_protectionism
				OR = {
					ig:ig_industrialists = {
						is_powerful = yes
					}
					ig:ig_rural_folk = {
						is_powerful = yes
					}
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.6_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.6
						}
					}
					set_global_variable = {
						name = bbg_common_6_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_7_cooldown
				}
				has_law = law_type:law_right_of_assembly
				ig:ig_trade_unions = {
					is_powerful = yes
				}
				ig:ig_rural_folk = {
					is_powerful = yes
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.7_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.7
						}
					}
					set_global_variable = {
						name = bbg_common_7_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_8_cooldown
				}
			}
			random_list = {
				50 = {
					# 假设每月有5%的概率触发
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.8_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.8
						}
					}
					set_global_variable = {
						name = bbg_common_8_cooldown
						days = 365							# 冷却时间为一年
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_9_cooldown
				}
				any_scope_state = {
					has_building = building_motor_industry
				}
				OR = {
					ig:ig_trade_unions = {
						is_powerful = yes
					}
					ig:ig_industrialists = {
						is_powerful = yes
					}
				}
			}
			random_list = {
				50 = {
					# 每月有5%的概率触发
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.9_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.9
						}
					}
					set_global_variable = {
						name = bbg_common_9_cooldown
						days = 365						# 冷却时间为一年
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_10_cooldown
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.10_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.10
						}
					}
					set_global_variable = {
						name = bbg_common_10_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_11_cooldown
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.11_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.11
						}
					}
					set_global_variable = {
						name = bbg_common_11_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
			limit = {
				NOT = {
					has_global_variable = bbg_common_12_cooldown
				}
			}
			random_list = {
				50 = {
					if = {
						limit = {
							has_global_variable = bbg_use_short_events
						}
						trigger_event = {
							id = bbg_common.12_short
						}
					}
					else = {
						trigger_event = {
							id = bbg_common.12
						}
					}
					set_global_variable = {
						name = bbg_common_12_cooldown
						days = 365
					}
				}
				50 = {
				}
			}
		}
		if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_13_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.13_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.13
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_13_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_14_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.14_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.14
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_14_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_15_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.15_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.15
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_15_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_16_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.16_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.16
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_16_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_17_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.17_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.17
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_17_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_18_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.18_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.18
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_18_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_19_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.19_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.19
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_19_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common_20_cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.20_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.20
                        }
                    }
                    set_global_variable = {
                        name = bbg_common_20_cooldown
                        days = 365
                    }
                }
                50 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.25.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.25
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.26.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.26
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.27.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.27
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.28.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.28
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.29.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.29
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.30.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.30
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.31.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.31
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.32.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.32
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.33.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.33
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.34.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.34
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.35.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.35
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.36.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.36
                    }
                }
                70 = {
                }
            }
        }
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.37.cooldown
                }
            }
            random_list = {
                30 = {
                    trigger_event = {
                        id = bbg_common.37
                    }
                }
                70 = {
                }
            }
        }

        # 新增事件38-50的每月判定
        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.38.cooldown
                }
                has_technology_researched = steam_engine
                NOT = { has_technology_researched = steel_mills }
                any_scope_building = {
                    is_building_type = building_textile_mills
                    level >= 3
                }
                country_rank >= 15
                year >= 1820
                year <= 1860
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.38
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.39.cooldown
                }
                has_technology_researched = railways
                gdp >= 50000000
                any_scope_state = {
                    infrastructure < 15
                    market_access >= 0.7
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.39
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.40.cooldown
                }
                has_law = law_autocracy
                OR = {
                    has_law = law_oligarchy
                    has_law = law_landed_voting
                }
                radical_fraction >= 0.15
                any_interest_group = {
                    is_interest_group_type = ig_intelligentsia
                    ig_approval <= -5
                }
                year >= 1830
                year <= 1870
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.40
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.41.cooldown
                }
                has_technology_researched = empiricism
                OR = {
                    has_law = law_public_schools
                    has_law = law_private_schools
                }
                ig:ig_intelligentsia = {
                    is_powerful = yes
                }
                ig:ig_devout = {
                    is_marginal = no
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.41
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.42.cooldown
                }
                has_technology_researched = mechanical_production
                any_scope_state = {
                    OR = {
                        has_building = building_textile_mills
                        has_building = building_steel_mills
                        has_building = building_chemical_plants
                    }
                    state_has_building_levels = 20
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.42
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.43.cooldown
                }
                has_technology_researched = currency_standards
                any_scope_state = {
                    has_building = building_financial_district
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.43
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.44.cooldown
                }
                has_technology_researched = navigation
                any_scope_state = {
                    OR = {
                        has_building = building_port
                        has_building = building_trade_center
                    }
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.44
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.45.cooldown
                }
                has_technology_researched = military_statistics
                ig:ig_armed_forces = {
                    is_marginal = no
                }
                any_scope_state = {
                    has_building = building_barracks
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.45
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.46.cooldown
                }
                has_technology_researched = urban_planning
                any_scope_state = {
                    state_urbanization >= 20
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.46
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.47.cooldown
                }
                has_technology_researched = banking
                treasury < -50000
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.47
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.48.cooldown
                }
                has_technology_researched = empiricism
                OR = {
                    has_law = law_public_schools
                    has_law = law_private_schools
                }
                ig:ig_intelligentsia = {
                    is_powerful = yes
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.48
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.49.cooldown
                }
                has_technology_researched = railways
                gdp >= 75000000
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.49
                    }
                }
                75 = {
                }
            }
        }

        if = {
            limit = {
                NOT = {
                    has_global_variable = bbg_common.50.cooldown
                }
                any_scope_state = {
                    OR = {
                        has_building = building_rye_farm
                        has_building = building_wheat_farm
                        has_building = building_rice_farm
                    }
                }
            }
            random_list = {
                25 = {
                    trigger_event = {
                        id = bbg_common.50
                    }
                }
                75 = {
                }
            }
        }

        # 事件26检测
        if = {
            limit = {
                has_technology_researched = banking
                any_scope_state = {
                    has_building = building_financial_district
                }
                NOT = {
                    has_global_variable = bbg_common.26.cooldown
                }
            }
            random_list = {
                5 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.26_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.26
                        }
                    }
                }
                95 = {
                }
            }
        }

        # 事件27检测
        if = {
            limit = {
                has_technology_researched = navigation
                any_scope_state = {
                    OR = {
                        has_building = building_port
                        has_building = building_trade_center
                    }
                }
                NOT = {
                    has_global_variable = bbg_common.27.cooldown
                }
            }
            random_list = {
                5 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.27_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.27
                        }
                    }
                }
                95 = {
                }
            }
        }

        # 事件28检测
        if = {
            limit = {
                has_technology_researched = currency_standards
                any_scope_state = {
                    has_building = building_financial_district
                }
                NOT = {
                    has_global_variable = bbg_common.28.cooldown
                }
            }
            random_list = {
                5 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.28_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.28
                        }
                    }
                }
                95 = {
                }
            }
        }

        # 事件29检测
        if = {
            limit = {
                has_technology_researched = international_trade
                NOT = {
                    has_global_variable = bbg_common.29.cooldown
                }
            }
            random_list = {
                3 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.29_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.29
                        }
                    }
                }
                97 = {
                }
            }
        }

        # 事件30检测
        if = {
            limit = {
                has_technology_researched = international_trade
                NOT = {
                    has_global_variable = bbg_common.30.cooldown
                }
            }
            random_list = {
                3 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.30_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.30
                        }
                    }
                }
                97 = {
                }
            }
        }

        # 事件31检测
        if = {
            limit = {
                has_technology_researched = empiricism
                any_scope_state = {
                    has_building = building_university
                }
                NOT = {
                    has_global_variable = bbg_common.31.cooldown
                }
            }
            random_list = {
                4 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.31_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.31
                        }
                    }
                }
                96 = {
                }
            }
        }

        # 事件32检测
        if = {
            limit = {
                has_technology_researched = mechanical_production
                any_scope_state = {
                    has_building = building_tooling_workshops
                }
                NOT = {
                    has_global_variable = bbg_common.32.cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.32_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.32
                        }
                    }
                }
                50 = {
                }
            }
        }

        # 事件33检测
        if = {
            limit = {
                has_technology_researched = empiricism
                any_scope_state = {
                    has_building = building_university
                }
                NOT = {
                    has_global_variable = bbg_common.33.cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.33_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.33
                        }
                    }
                }
                50 = {
                }
            }
        }

        # 事件34检测
        if = {
            limit = {
                has_technology_researched = mechanical_production
                any_scope_state = {
                    has_building = building_tooling_workshops
                }
                NOT = {
                    has_global_variable = bbg_common.34.cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.34_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.34
                        }
                    }
                }
                50 = {
                }
            }
        }

        # 事件35检测
        if = {
            limit = {
                has_technology_researched = urban_planning
                any_scope_state = {
                    has_building = building_urban_center
                }
                NOT = {
                    has_global_variable = bbg_common.35.cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.35_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.35
                        }
                    }
                }
                50 = {
                }
            }
        }

        # 事件36检测
        if = {
            limit = {
                has_technology_researched = mechanical_production
                any_scope_state = {
                    has_building = building_tooling_workshops
                }
                NOT = {
                    has_global_variable = bbg_common.36.cooldown
                }
            }
            random_list = {
                50 = {
                    if = {
                        limit = {
                            has_global_variable = bbg_use_short_events
                        }
                        trigger_event = {
                            id = bbg_common.36_short
                        }
                    }
                    else = {
                        trigger_event = {
                            id = bbg_common.36
                        }
                    }
                }
                50 = {
                }
            }
        }


        # 事件37-40检测
        # 事件37：铁路工人罢工
        if = {
            limit = {
                has_technology_researched = railways
                NOT = { has_global_variable = bbg_common.37.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.37_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.37 }
                    }
                }
                50 = { }
            }
        }

        # 事件38：科学与宗教冲突
        if = {
            limit = {
                has_technology_researched = empiricism
                NOT = { has_global_variable = bbg_common.38.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.38_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.38 }
                    }
                }
                50 = { }
            }
        }

        # 事件39：贸易保护主义争议
        if = {
            limit = {
                has_technology_researched = international_trade
                NOT = { has_global_variable = bbg_common.39.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.39_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.39 }
                    }
                }
                50 = { }
            }
        }

        # 事件40：大学改革争议
        if = {
            limit = {
                has_technology_researched = empiricism
                NOT = { has_global_variable = bbg_common.40.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.40_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.40 }
                    }
                }
                50 = { }
            }
        }

        # 事件41-50检测
        # 事件41：银行业改革争议
        if = {
            limit = {
                has_technology_researched = banking
                NOT = { has_global_variable = bbg_common.41.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.41_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.41 }
                    }
                }
                50 = { }
            }
        }

        # 事件42：电力时代的到来
        if = {
            limit = {
                has_technology_researched = electrical_generation
                NOT = { has_global_variable = bbg_common.42.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.42_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.42 }
                    }
                }
                50 = { }
            }
        }

        # 事件43：劳工运动兴起
        if = {
            limit = {
                has_technology_researched = labor_movement
                NOT = { has_global_variable = bbg_common.43.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.43_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.43 }
                    }
                }
                50 = { }
            }
        }

        # 事件44：民族主义浪潮
        if = {
            limit = {
                has_technology_researched = nationalism
                NOT = { has_global_variable = bbg_common.44.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.44_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.44 }
                    }
                }
                50 = { }
            }
        }

        # 事件45：医学教育改革
        if = {
            limit = {
                has_technology_researched = medical_degrees
                NOT = { has_global_variable = bbg_common.45.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.45_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.45 }
                    }
                }
                50 = { }
            }
        }
        # 事件46：铁路网络扩张
        if = {
            limit = {
                has_technology_researched = railways
                NOT = { has_global_variable = bbg_common.46.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.46_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.46 }
                    }
                }
                50 = { }
            }
        }

        # 事件47：军事统计学应用
        if = {
            limit = {
                has_technology_researched = military_statistics
                NOT = { has_global_variable = bbg_common.47.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.47_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.47 }
                    }
                }
                50 = { }
            }
        }

        # 事件48：工人组织化运动
        if = {
            limit = {
                has_technology_researched = labor_movement
                NOT = { has_global_variable = bbg_common.48.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.48_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.48 }
                    }
                }
                50 = { }
            }
        }

        # 事件49：专利权保护争议
        if = {
            limit = {
                has_technology_researched = patent_rights
                NOT = { has_global_variable = bbg_common.49.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.49_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.49 }
                    }
                }
                50 = { }
            }
        }

        # 事件50：政治煽动与言论自由
        if = {
            limit = {
                has_technology_researched = political_agitation
                NOT = { has_global_variable = bbg_common.50.cooldown }
            }
            random_list = {
                50 = {
                    if = {
                        limit = { has_global_variable = bbg_use_short_events }
                        trigger_event = { id = bbg_common.50_short }
                    }
                    else = {
                        trigger_event = { id = bbg_common.50 }
                    }
                }
                50 = { }
            }
        }
	}
}

abandon_revolution - 放弃革命
activate_building - 在某个州激活一座建筑
activate_law - 为一个国家激活一项法律
activate_production_method - 在国家/州为某种类型的建筑激活指定的生产方式
add_acceptance - 为某个州的某种文化增加接受度增量
add_arable_land - 为某个州区增加/移除可耕地
add_banned_goods - 为一个国家增加一种商品的完全禁令
add_change_relations_progress - 为改善两国关系增加进度
add_character_ideology - 为范围内的政治运动添加一个人物意识形态
add_character_role - 为一个人物添加一个新的角色
add_civil_war_progress - 为内战进度增加指定的百分点(范围是[0, 1]，0.1表示10个百分点)
add_claim - 将范围内的州区添加为目标国家的宣称
add_cohesion_number - 为范围内的权力集团增加特定数量的凝聚力
add_cohesion_percent - 为范围内的权力集团增加基于百分比的凝聚力
add_commander_rank - 将一个人物提升/降低指定的军衔等级
add_company - 为一个国家的公司增加公司类型
add_cultural_community - 在范围内州为目标文化添加一个文化社群
add_cultural_community_in_state - 在目标州为范围内文化添加一个文化社群
add_cultural_obsession - 为范围内的文化增加一个新的痴迷
add_culture_standard_of_living_modifier - 在范围内州为给定文化应用一个生活水平修正。除了必需的文化参数外，此效果的语法与add_modifier相同
add_declared_interest - 将在目标战略区域创建一个已宣布的利益
add_devastation - 为一个州区增加/移除荒芜度
add_diplomatic_play_war_support - 在范围内的外交博弈中为目标国家增加战争支持度。该数值将显示在工具提示的“情况”标题下
add_enactment_modifier - 为范围内的对象添加一个与颁布相关的计时修正效果
add_enactment_phase - 将范围内国家的当前法律颁布阶段增加一个量。结果将被限制在0和NPolitics::LAW_ENACTMENT_MAX_PHASES之间。如果结果值等于NPolitics::LAW_ENACTMENT_MAX_PHASES，则颁布的法律将通过
add_enactment_setback - 将范围内国家的当前法律颁布挫折计数增加一个量。结果将被限制在0和country_law_max_setbacks_add之间。如果结果值等于country_law_max_setbacks_add，则法律颁布将失败
add_era_researched - 将指定时代添加为范围内国家已研究的时代
add_escalation - 为外交博弈增加升级
add_experience - 为指挥官增加经验值
add_homeland - 将范围内的州区添加为目标文化的家园
add_ideology - 为范围内的利益集团增加一个意识形态
add_ig_to_party - 将目标利益集团添加到范围内的政党
add_initiator_backers - 将一个标签/范围国家添加到外交博弈的发起方
add_investment_pool - 直接向投资池增加资金
add_journal_entry - 为一个范围国家的日志添加一个日志条目，可选择保存范围目标
add_law_progress - 为正在通过的法律的当前检查点增加x%的进度(范围是[0, 1]，0.1表示10个百分点)
add_leverage - 为范围内的权力集团对指定国家增加指定数量的影响力
add_liberty_desire - 为一个附属国增加自由渴望。如果范围内的国家不是外交契约中的附属国，则无效
add_lobby_member - 将一个利益集团添加为范围政治游说团体的成员
add_loyalists - 为范围国家的民众增加支持者。除value外的所有参数都是可选的
add_loyalists_in_state - 为范围州的民众增加支持者。除value外的所有参数都是可选的
add_modifier - 为范围内的对象添加一个计时修正效果
add_momentum - 在竞选期间为一个政党增加势头
add_morale - 为范围内的战斗单位增加指定数量的士气
add_organization - 为范围内的军事编队增加指定数量的组织度
add_pollution - 增加/减少范围内州区的污染水平
add_pop_wealth - 增加民众的财富
add_primary_culture - 为一个国家的主要文化增加一种文化
add_principle - 为权力集团增加原则
add_progress - 为日志条目的进度条增加进度
add_radicals - 为范围国家的民众增加激进分子。除value外的所有参数都是可选的
add_radicals_in_state - 为范围州的民众增加激进分子。除value外的所有参数都是可选的
add_random_trait - 添加一个指定类别的随机合格特质
add_religion_standard_of_living_modifier - 在范围内州为给定宗教应用一个生活水平修正。除了必需的宗教参数外，此效果的语法与add_modifier相同
add_ruling_interest_group - 将利益集团添加到政府中
add_state_trait - 在一个范围内的州区添加州特质
add_target_backers - 将一个标签/范围国家添加到外交博弈的目标方
add_taxed_goods - 为一个国家增加一种商品的消费税
add_technology_progress - 增加科技进度
add_technology_researched - 在一个国家范围内研究指定的技术
add_to_global_variable_list - 将事件目标添加到一个变量列表中
add_to_list - 将当前范围添加到一个任意命名的列表中(如果不存在则创建列表)，以便在(未中断的)事件链中稍后引用
add_to_local_variable_list - 将事件目标添加到一个变量列表中
add_to_temporary_list - 将当前范围添加到一个任意命名的列表中(如果不存在则创建列表)，以便在同一效果中稍后引用
add_to_variable_list - 将事件目标添加到一个变量列表中
add_trait - 为一个人物添加一个特质
add_treasury - 为一个国家增加/移除资金
add_war_exhaustion - 为范围战争中的目标国家增加战争疲劳度。该数值将显示在工具提示的“情况”标题下
add_war_goal - 为一个外交博弈添加一个战争目标。与create_diplomatic_play中的add_war_goal读取的数据相同
add_war_war_support - 为范围战争中的目标国家增加战争支持度。该数值将显示在工具提示的“情况”标题下
annex - 吞并一个国家
annex_as_civil_war - 吞并一个国家，并继承内战胜利方的所有继承效果
annex_with_incorporation - 吞并一个国家，并继承其州的整合状态
assert_if - 在运行时有条件地引发断言
assert_read - 在读取时有条件地引发断言
call_election - 在N个月内为国家设定下一次选举日期
cancel_enactment - 停止颁布国家当前正在颁布的法律
cancel_imposition - 取消范围内法律(而非法律类型)的强制实施
change_appeasement - 改变范围政治游说团体的安抚度
change_character_culture - 改变范围内人物的文化
change_character_religion - 改变范围内人物的宗教
change_global_variable - 改变一个数值变量的值
change_infamy - 改变范围国家的恶名
change_institution_investment_level - 增加/移除机构的投资水平
change_local_variable - 改变一个数值变量的值
change_pop_culture - 将范围内民众的文化按指定百分比改变为指定文化
change_pop_religion - 将范围内民众的宗教按指定百分比改变为指定宗教
change_poptype - 将民众的类型改变为给定类型
change_relations - 改变两国之间的关系
change_subject_type - 改变范围内国家的附属国类型，同时保留当前的自由渴望值
change_tag - 改变范围国家的游戏标签
change_tension - 改变两国之间的紧张关系
change_variable - 改变一个数值变量的值
clamp_global_variable - 将一个变量限制在指定的最大值和最小值之间
clamp_local_variable - 将一个变量限制在指定的最大值和最小值之间
clamp_variable - 将一个变量限制在指定的最大值和最小值之间
clear_debt - 清除国家贷款
clear_enactment_modifier - 清除范围国家当前的法律颁布修正
clear_global_variable_list - 清空列表
clear_local_variable_list - 清空列表
clear_saved_scope - 从顶层范围中清除一个已保存的范围
clear_scaled_debt - 清除相当于目标最大信贷额度上定义乘数的债务
clear_variable_list - 清空列表
complete_objective_subgoal - 完成一个目标的子目标
convert_population - 将X%的不同宗教人口改变为指定宗教
copy_laws - 复制目标范围国家的宪法
create_bidirectional_truce - 在两国之间建立双向休战
create_building - 在范围内州创建一座建筑
create_character - 创建一个人物，任何选项都可以省略
create_country - 创建一个新的国家
create_diplomatic_catalyst - 创建一个新的外交催化剂
create_diplomatic_pact - 在两国之间建立一个外交契约，以范围国家为发起方
create_diplomatic_play - 创建一个以外交博弈为目标的外交博弈
create_dynamic_country - 创建一个带有动态标签的新国家
create_incident - 创建一个产生恶名的外交事件，以目标国家为受害者
create_mass_migration - 从一个来源国向一个范围州发起特定文化的大规模移民
create_military_formation - 创建一个军事编队
create_political_lobby - 创建一个新的政治游说团体
create_political_movement - 在国家内创建指定类型的政治运动，文化/宗教可选
create_pop - 在范围内州创建一个民众单位
create_power_bloc - 创建一个以范围对象为领导者的权力集团
create_state - 在一个州区创建一个州
create_trade_route - 创建一个新的贸易路线
create_unidirectional_truce - 为一个国家对另一个国家创建单向休战
custom_description - 包装那些获得自定义描述而非自动生成描述的效果
custom_description_no_bullet - 包装那些获得自定义描述而非自动生成描述的效果。同时确保不出现项目符号
custom_label - 只是一个工具提示，范围作为对象(用于分组，本地化)
custom_tooltip - 只是一个工具提示，范围作为主题(用于分组，本地化)
deactivate_building - 在一个州停用一座建筑
deactivate_law - 为一个国家停用一项法律
deactivate_parties - 在范围国家内停用政党
debug_log - 当此效果执行时，向调试日志记录一个字符串
debug_log_scopes - 当此效果执行时，将当前范围记录到调试日志
decrease_autonomy - 将一个国家的附属国类型改为自主性更低的类型
deploy_to_front - 将范围编队部署到目标前线
disband_party - 从政党中移除所有利益集团，导致其解散
disband_political_lobby - 解散范围内的政治游说团体
disinherit_character - 剥夺范围内人物在所有适用国家的继承人地位
else - 如果前面的'if'或'else_if'的限制条件不满足，则执行包含的效果
else_if - 如果前面的'if'或'else_if'的限制条件不满足，并且其自身的限制条件满足，则执行包含的效果
end_play - 结束一场外交博弈
end_truce - 结束两国之间的任何休战
every_active_law - 遍历一个国家的所有现行法律
every_active_party - 遍历一个国家的所有活跃政党
every_character - 遍历全球所有人物
every_character_in_exile_pool - 遍历流亡池中的所有人物
every_character_in_void - 遍历虚空中的所有人物
every_civil_war - 遍历与范围国家相关的所有内战
every_cobelligerent_in_diplo_play - 遍历范围国家在所有外交博弈(包括战争)中的所有共同交战方
every_cobelligerent_in_war - 遍历范围国家在所有战争中的所有共同交战方
every_combat_unit - 遍历输入范围内的所有战斗单位
every_company - 遍历一个国家的所有公司
every_country - 遍历全球所有国家
every_diplomatic_catalyst - 遍历一个国家近期记忆中的所有外交催化剂
every_diplomatic_play - 遍历全球所有外交博弈
every_diplomatically_relevant_country - 遍历一个国家范围内的所有外交相关国家
every_direct_subject - 当前层级中任何直接下属的国家
every_enemy_in_diplo_play - 遍历范围国家在所有外交博弈(包括战争)中的所有敌人
every_enemy_in_war - 遍历范围国家在所有战争中的所有敌人
every_harvest_condition - 遍历影响一个州、州区、战略区域或国家的所有收获条件
every_in_global_list - 遍历全局列表中的所有项目
every_in_hierarchy - 当前层级中的任何国家，包括当前国家
every_in_list - 遍历列表中的所有项目
every_in_local_list - 遍历本地列表中的所有项目
every_influenced_interest_group - 遍历受政治运动影响的所有利益集团
every_interest_group - 遍历一个国家的所有利益集团
every_law - 遍历一个国家的所有法律
every_lobby_member - 遍历一个游说团体的所有利益集团成员
every_market - 遍历全球所有市场
every_market_goods - 遍历一个市场中的所有活跃(市场)商品
every_member - 遍历一个政党的所有利益集团成员
every_military_formation - 遍历输入范围内当前存在的所有军事编队
every_neighbouring_state - 遍历一个州区的所有相邻州
every_overlord_or_above - 当前层级中任何上级国家
every_participant - 范围内外交契约的任意两个参与方
every_political_lobby - 遍历一个国家或利益集团的所有政治游说团体
every_political_movement - 遍历一个国家的所有政治运动
every_potential_party - 遍历一个国家的所有潜在政党
every_power_bloc - 遍历所有权力集团
every_power_bloc_member - 遍历范围权力集团的所有成员，包括领导者
every_preferred_law - 遍历一个利益集团国家的所有现行和可能的法律，按他们偏好该法律的程度排序
every_primary_culture - 范围国家或国家定义的主要文化
every_province - 遍历范围州的所有省份
every_rival_country - 任何被范围内国家视为对手的国家
every_rivaling_country - 任何将范围内国家视为对手的国家
every_scope_admiral - 遍历一个国家、利益集团或军事编队中的所有海军将领
every_scope_ally - 遍历一个国家的所有盟友
every_scope_building - 遍历一个州、国家的所有建筑
every_scope_character - 遍历一个国家、利益集团或前线的所有人物
every_scope_country - 遍历在支持的范围内(目前是：市场、战略区域)有存在的国家
every_scope_culture - 遍历范围内的所有文化
every_scope_diplomatic_pact - 范围内国家的任何外交契约
every_scope_front - 遍历范围战争相关的所有前线
every_scope_general - 遍历一个国家、利益集团、前线或军事编队中的所有将军
every_scope_held_interest_marker - 遍历一个国家持有的所有利益标记
every_scope_initiator_ally - 遍历一个外交博弈中发起方的所有盟友
every_scope_interest_marker - 遍历一个国家、战略区域中的所有利益标记
every_scope_play_involved - 遍历一个外交博弈中的所有参与方
every_scope_politician - 遍历一个国家或利益集团的所有政治家
every_scope_pop - 遍历一个国家、州、利益集团、文化的所有民众
every_scope_state - 遍历一个国家、州区、战区或前线的所有州(包括省份)
every_scope_target_ally - 遍历一个外交博弈中目标方的所有盟友
every_scope_theater - 遍历一个国家的所有战区
every_scope_violate_sovereignty_interested_parties - 遍历所有如果范围内国家主权被侵犯会感兴趣的国家
every_scope_violate_sovereignty_wars - 遍历如果目标国家主权被范围国家侵犯时的所有相关战争
every_scope_war - 遍历与范围相关的所有战争
every_sea_node_adjacent_state - 遍历与一个州共享一个海道的所有州
every_state - 遍历全球所有州
every_state_region - 遍历所有州区
every_strategic_objective - 遍历范围国家的所有战略目标州
every_subject_of_subject - 层级中直接附属国以下的任何国家
every_subject_or_below - 当前层级以下的任何国家
every_supporting_character - 遍历支持范围政治运动的所有人物
every_trade_route - 遍历一个市场、国家、市场商品中的所有贸易路线
every_valid_mass_migration_culture - 列出范围内国家中适合大规模移民的文化
every_war_participant - 遍历一场战争的所有参与方
exile_character - 将一个人物流放到流亡池
finish_harvest_condition - 在范围州区完成某种类型的收获条件
force_resource_depletion - 强制一个州的资源枯竭
force_resource_discovery - 强制一个州的资源发现
free_character_from_void - 将一个人物从虚空中释放，如果设置为no，则人物被删除
fully_mobilize_army - 完全动员范围军队
hidden_effect - 包含的效果在工具提示中不显示
if - 如果满足限制条件，则执行包含的效果
increase_autonomy - 将一个国家的附属国类型改为更自主的类型
join_power_bloc - 范围国家加入目标范围国家的权力集团
join_revolution - 将利益集团加入正在进行的革命
join_war - 使目标加入一场范围战争的特定一方
kill_character - 杀死一个人物
kill_population - 在范围国家杀死一定数量的民众
kill_population_in_state - 在范围州杀死一定数量的民众
kill_population_percent - 在范围国家杀死一定百分比的民众
kill_population_percent_in_state - 在范围州杀死一定百分比的民众
lock_trade_route - 在一段时间内锁定一条贸易路线，防止其被手动取消
make_independent - 使一个国家独立
mobilize_army - 动员范围军队
move_partial_pop - 将范围民众的一部分移动到指定州(他们会失业)
move_pop - 将范围民众整体移动到指定州(他们会失业)
ordered_active_law - 按顺序遍历一个国家的所有现行法律
ordered_active_party - 按顺序遍历一个国家的所有活跃政党
ordered_character - 按顺序遍历全球所有人物
ordered_character_in_exile_pool - 按顺序遍历流亡池中的人物
ordered_character_in_void - 按顺序遍历虚空中的人物
ordered_civil_war - 按顺序遍历与范围国家相关的所有内战
ordered_cobelligerent_in_diplo_play - 按顺序遍历范围国家在所有外交博弈(包括战争)中的所有共同交战方
ordered_cobelligerent_in_war - 按顺序遍历范围国家在所有战争中的所有共同交战方
ordered_combat_unit - 按顺序遍历输入范围内的所有战斗单位
ordered_company - 按顺序遍历一个国家的所有公司
ordered_country - 按顺序遍历全球所有国家
ordered_diplomatic_catalyst - 按顺序遍历一个国家近期记忆中的所有外交催化剂
ordered_diplomatic_play - 按顺序遍历全球所有外交博弈
ordered_diplomatically_relevant_country - 按顺序遍历一个国家范围内的所有外交相关国家
ordered_direct_subject - 当前层级中任何直接下属的国家
ordered_enemy_in_diplo_play - 按顺序遍历范围国家在所有外交博弈(包括战争)中的所有敌人
ordered_enemy_in_war - 按顺序遍历范围国家在所有战争中的所有敌人
ordered_harvest_condition - 按顺序遍历影响一个州、州区、战略区域或国家的所有收获条件
ordered_in_global_list - 按顺序遍历全局列表中的所有项目
ordered_in_hierarchy - 当前层级中的任何国家，包括当前国家
ordered_in_list - 按顺序遍历列表中的所有项目
ordered_in_local_list - 按顺序遍历本地列表中的所有项目
ordered_influenced_interest_group - 按顺序遍历受政治运动影响的所有利益集团
ordered_interest_group - 按顺序遍历一个国家的所有利益集团
ordered_law - 按顺序遍历一个国家的所有法律
ordered_lobby_member - 按顺序遍历一个游说团体的所有利益集团成员
ordered_market - 按顺序遍历全球所有市场
ordered_market_goods - 按顺序遍历一个市场中的所有活跃(市场)商品
ordered_member - 按顺序遍历一个政党的所有利益集团成员
ordered_military_formation - 按顺序遍历输入范围内当前存在的所有军事编队
ordered_neighbouring_state - 按顺序遍历一个州区的所有相邻州
ordered_overlord_or_above - 当前层级中任何上级国家
ordered_participant - 范围内外交契约的任意两个参与方
ordered_political_lobby - 按顺序遍历一个国家或利益集团的所有政治游说团体
ordered_political_movement - 按顺序遍历一个国家的所有政治运动
ordered_potential_party - 按顺序遍历一个国家的所有潜在政党
ordered_power_bloc - 按顺序遍历所有权力集团
ordered_power_bloc_member - 按顺序遍历范围权力集团的所有成员，包括领导者
ordered_preferred_law - 按顺序遍历一个利益集团国家的所有现行和可能的法律，按他们偏好该法律的程度排序
ordered_primary_culture - 范围国家或国家定义的主要文化
ordered_province - 按顺序遍历范围州的所有省份
ordered_rival_country - 任何被范围内国家视为对手的国家
ordered_rivaling_country - 任何将范围内国家视为对手的国家
ordered_scope_admiral - 按顺序遍历一个国家、利益集团或军事编队中的所有海军将领
ordered_scope_ally - 按顺序遍历一个国家的所有盟友
ordered_scope_building - 按顺序遍历一个州、国家的所有建筑
ordered_scope_character - 按顺序遍历一个国家、利益集团或前线的所有人物
ordered_scope_country - 按顺序遍历在支持的范围内(目前是：市场、战略区域)有存在的国家
ordered_scope_culture - 按顺序遍历范围内的所有文化
ordered_scope_diplomatic_pact - 范围内国家的任何外交契约
ordered_scope_front - 按顺序遍历范围战争相关的所有前线
ordered_scope_general - 按顺序遍历一个国家、利益集团、前线或军事编队中的所有将军
ordered_scope_held_interest_marker - 按顺序遍历一个国家持有的所有利益标记
ordered_scope_initiator_ally - 按顺序遍历一个外交博弈中发起方的所有盟友
ordered_scope_interest_marker - 按顺序遍历一个国家、战略区域中的所有利益标记
ordered_scope_play_involved - 按顺序遍历一个外交博弈中的所有参与方
ordered_scope_politician - 按顺序遍历一个国家或利益集团的所有政治家
ordered_scope_pop - 按顺序遍历一个国家、州、利益集团、文化的所有民众
ordered_scope_state - 按顺序遍历一个国家、州区、战区或前线的所有州(包括省份)
ordered_scope_target_ally - 按顺序遍历一个外交博弈中目标方的所有盟友
ordered_scope_theater - 按顺序遍历一个国家的所有战区
ordered_scope_violate_sovereignty_interested_parties - 按顺序遍历所有如果范围内国家主权被侵犯会感兴趣的国家
ordered_scope_violate_sovereignty_wars - 按顺序遍历如果目标国家主权被范围国家侵犯时的所有相关战争
ordered_scope_war - 按顺序遍历与范围相关的所有战争
ordered_sea_node_adjacent_state - 按顺序遍历与一个州共享一个海道的所有州
ordered_state - 按顺序遍历全球所有州
ordered_state_region - 按顺序遍历所有州区
ordered_strategic_objective - 按顺序遍历范围国家的所有战略目标州
ordered_subject_of_subject - 层级中直接附属国以下的任何国家
ordered_subject_or_below - 当前层级以下的任何国家
ordered_supporting_character - 按顺序遍历支持范围政治运动的所有人物
ordered_trade_route - 按顺序遍历一个市场、国家、市场商品中的所有贸易路线
ordered_valid_mass_migration_culture - 按顺序列出范围内国家中适合大规模移民的文化
ordered_war_participant - 按顺序遍历一场战争的所有参与方
place_character_in_void - 将一个人物放逐到虚空，duration是人物在被删除前保留的时间
play_as - 改变范围国家的玩家将扮演哪个国家
post_notification - 发布通知
post_proposal - 发布提案
random - 根据随机几率运行一个效果，否则什么也不做
random_active_law - 随机遍历一个国家的所有现行法律
random_active_party - 随机遍历一个国家的所有活跃政党
random_character - 随机遍历全球所有人物
random_character_in_exile_pool - 随机遍历流亡池中的人物
random_character_in_void - 随机遍历虚空中的人物
random_civil_war - 随机遍历与范围国家相关的所有内战
random_cobelligerent_in_diplo_play - 随机遍历范围国家在所有外交博弈(包括战争)中的所有共同交战方
random_cobelligerent_in_war - 随机遍历范围国家在所有战争中的所有共同交战方
random_combat_unit - 随机遍历输入范围内的所有战斗单位
random_company - 随机遍历一个国家的所有公司
random_country - 随机遍历全球所有国家
random_diplomatic_catalyst - 随机遍历一个国家近期记忆中的所有外交催化剂
random_diplomatic_play - 随机遍历全球所有外交博弈
random_diplomatically_relevant_country - 随机遍历一个国家范围内的所有外交相关国家
random_direct_subject - 当前层级中任何直接下属的国家
random_enemy_in_diplo_play - 随机遍历范围国家在所有外交博弈(包括战争)中的所有敌人
random_enemy_in_war - 随机遍历范围国家在所有战争中的所有敌人
random_harvest_condition - 随机遍历影响一个州、州区、战略区域或国家的所有收获条件
random_in_global_list - 随机遍历全局列表中的所有项目
random_in_hierarchy - 当前层级中的任何国家，包括当前国家
random_in_list - 随机遍历列表中的所有项目
random_in_local_list - 随机遍历本地列表中的所有项目
random_influenced_interest_group - 随机遍历受政治运动影响的所有利益集团
random_interest_group - 随机遍历一个国家的所有利益集团
random_law - 随机遍历一个国家的所有法律
random_list - 一个随机列表效果
random_lobby_member - 随机遍历一个游说团体的所有利益集团成员
random_log_scopes - 当此效果执行时，将当前范围记录到随机日志
random_market - 随机遍历全球所有市场
random_market_goods - 随机遍历一个市场中的所有活跃(市场)商品
random_member - 随机遍历一个政党的所有利益集团成员
random_military_formation - 随机遍历输入范围内当前存在的所有军事编队
random_neighbouring_state - 随机遍历一个州区的所有相邻州
random_overlord_or_above - 当前层级中任何上级国家
random_participant - 范围内外交契约的任意两个参与方
random_political_lobby - 随机遍历一个国家或利益集团的所有政治游说团体
random_political_movement - 随机遍历一个国家的所有政治运动
random_potential_party - 随机遍历一个国家的所有潜在政党
random_power_bloc - 随机遍历所有权力集团
random_power_bloc_member - 随机遍历范围权力集团的所有成员，包括领导者
random_preferred_law - 随机遍历一个利益集团国家的所有现行和可能的法律，按他们偏好该法律的程度排序
random_primary_culture - 范围国家或国家定义的主要文化
random_province - 随机遍历范围州的所有省份
random_rival_country - 任何被范围内国家视为对手的国家
random_rivaling_country - 任何将范围内国家视为对手的国家
random_scope_admiral - 随机遍历一个国家、利益集团或军事编队中的所有海军将领
random_scope_ally - 随机遍历一个国家的所有盟友
random_scope_building - 随机遍历一个州、国家的所有建筑
random_scope_character - 随机遍历一个国家、利益集团或前线的所有人物
random_scope_country - 随机遍历在支持的范围内(目前是：市场、战略区域)有存在的国家
random_scope_culture - 随机遍历范围内的所有文化
random_scope_diplomatic_pact - 范围内国家的任何外交契约
random_scope_front - 随机遍历范围战争相关的所有前线
random_scope_general - 随机遍历一个国家、利益集团、前线或军事编队中的所有将军
random_scope_held_interest_marker - 随机遍历一个国家持有的所有利益标记
random_scope_initiator_ally - 随机遍历一个外交博弈中发起方的所有盟友
random_scope_interest_marker - 随机遍历一个国家、战略区域中的所有利益标记
random_scope_play_involved - 随机遍历一个外交博弈中的所有参与方
random_scope_politician - 随机遍历一个国家或利益集团的所有政治家
random_scope_pop - 随机遍历一个国家、州、利益集团、文化的所有民众
random_scope_state - 随机遍历一个国家、州区、战区或前线的所有州(包括省份)
random_scope_target_ally - 随机遍历一个外交博弈中目标方的所有盟友
random_scope_theater - 随机遍历一个国家的所有战区
random_scope_violate_sovereignty_interested_parties - 随机遍历所有如果范围内国家主权被侵犯会感兴趣的国家
random_scope_violate_sovereignty_wars - 随机遍历如果目标国家主权被范围国家侵犯时的所有相关战争
random_scope_war - 随机遍历与范围相关的所有战争
random_sea_node_adjacent_state - 随机遍历与一个州共享一个海道的所有州
random_state - 随机遍历全球所有州
random_state_region - 随机遍历所有州区
random_strategic_objective - 随机遍历范围国家的所有战略目标州
random_subject_of_subject - 层级中直接附属国以下的任何国家
random_subject_or_below - 当前层级以下的任何国家
random_supporting_character - 随机遍历支持范围政治运动的所有人物
random_trade_route - 随机遍历一个市场、国家、市场商品中的所有贸易路线
random_valid_mass_migration_culture - 随机列出范围内国家中适合大规模移民的文化
random_war_participant - 随机遍历一场战争的所有参与方
recalculate_pop_ig_support - 重新计算并更新一个国家的民众利益集团支持度
regime_change - 由范围国家在目标国家执行政权更迭
remove_active_objective_subgoal - 移除一个活跃的目标子目标
remove_as_interest_group_leader - 将一个人物从利益集团领袖的位置上移除
remove_banned_goods - 从一个国家移除一种商品的完全禁令
remove_building - 在范围州移除一座建筑
remove_character_ideology - 从范围政治运动中移除一个人物意识形态
remove_character_role - 从一个人物中移除一个现有角色
remove_claim - 将范围州区从目标国家的宣称中移除
remove_company - 从一个国家的公司中移除公司类型
remove_cultural_obsession - 从范围文化中移除一个新的痴迷
remove_diplomatic_pact - 移除两国之间的外交契约，以范围国家为发起方
remove_enactment_modifier - 移除范围内对象的与颁布相关的计时修正效果
remove_from_list - 从一个命名列表中移除当前范围
remove_global_variable - 移除一个变量
remove_homeland - 将范围州区从目标文化的家园中移除
remove_ideology - 从范围利益集团中移除一个意识形态
remove_ig_from_party - 从范围政党中移除目标利益集团
remove_initiator_backers - 从外交博弈的发起方移除一个标签/范围国家
remove_list_global_variable - 从一个变量列表中移除目标
remove_list_local_variable - 从一个变量列表中移除目标
remove_list_variable - 从一个变量列表中移除目标
remove_lobby_member - 从范围政治游说团体中移除一个利益集团作为成员
remove_local_variable - 移除一个变量
remove_modifier - 移除范围内对象的计时修正效果
remove_primary_culture - 从一个国家的主要文化中移除一种文化
remove_principle - 从权力集团中移除原则
remove_ruling_interest_group - 从政府中移除范围内的利益集团
remove_state_trait - 在一个范围内的州区移除州特质
remove_target_backers - 从外交博弈的目标方移除一个标签/范围国家
remove_taxed_goods - 从一个国家移除一种商品的消费税
remove_trait - 从一个人物中移除一个特质
remove_variable - 移除一个变量
remove_war_goal - 从一个外交博弈中移除一个战争目标
reset_hub_names - 重置范围内州所有枢纽的名称
reset_state_name - 重置范围内州的名称
resolve_play_for - 效果结束一方的外交博弈，使其获得战争目标
round_global_variable - 将一个变量四舍五入到指定的最接近的值
round_local_variable - 将一个变量四舍五入到指定的最接近的值
round_variable - 将一个变量四舍五入到指定的最接近的值
save_scope_as - 将当前范围保存为一个任意命名的目标，以便在(未中断的)事件链中稍后引用
save_scope_value_as - 将一个数值或布尔值保存为一个任意命名的目标，以便在(未中断的)事件链中稍后引用
save_temporary_scope_as - 将当前范围保存为一个任意命名的临时目标，以便在同一效果中稍后引用
save_temporary_scope_value_as - 将一个数值或布尔值保存为一个任意命名的临时目标，以便在同一效果中稍后引用
seize_investment_pool - 为国库没收投资池，并将所有私人建设队列元素转移到政府队列
set_as_interest_group_leader - 将一个人物设为利益集团领袖
set_available_for_autonomous_investment - 将一种建筑类型设置为在当前范围州可用于自主投资
set_bar_progress - 为日志条目的脚本化进度条设置进度
set_bolstering - 开始/停止支持范围内的政治运动
set_capital - 在一个国家范围内设置首都州
set_character_as_ruler - 将范围内人物设为其国家的统治者
set_character_busy_and_immortal - 将一个人物标记为忙碌且不朽，或清除该标记
set_character_immortal - 将范围内人物设为不朽
set_commander_rank - 将一个人物提升/降低到一个给定的军衔值
set_company_establishment_date - 设置范围公司的成立日期
set_company_state_region - 设置范围公司的州区，以便公司建筑可以在那里建造
set_core_ideology - 设置一个政治运动的核心意识形态
set_country_type - 为一个国家设置国家类型，用于历史记录
set_devastation - 为一个州区设置荒芜度
set_diplomats_expelled - 从目标国家向范围国家驱逐外交官
set_global_variable - 设置一个变量
set_government_wage_level - 设置范围国家的政府工资水平
set_heir - 将范围国家的继承人设置为指定的人物范围
set_home_country - 设置一个人物的祖国。这会让他们开始认为自己被流放了
set_home_country_definition - 直接将一个人物的祖国设置为一个标签，这会让他们成为一个流亡者
set_hub_name - 将范围内州的一个枢纽的名称设置为一个本地化字符串
set_hub_names - 将范围内州所有枢纽的名称设置为基于州区名称、枢纽类型和指定后缀的本地化字符串
set_ideology - 改变范围内人物的意识形态
set_ig_trait - 为利益集团添加一个特质，或用相同支持度的特质替换他们当前的特质
set_immune_to_revolutions - 使一个国家对革命免疫或移除这种免疫
set_institution_investment_level - 设置一个机构的投资水平
set_interest_group - 设置人物的利益集团
set_interest_group_name - 将利益集团重命名为指定的本地化键
set_key - 为一个外交博弈设置名称
set_local_variable - 设置一个变量
set_market_capital - 在一个国家范围内设置市场首都
set_military_wage_level - 设置范围国家的军事工资水平
set_mutual_secret_goal - 为范围国家和目标国家设置共同的秘密AI目标
set_next_election_date - 为国家设置下一次选举日期
set_only_legal_party_from_ig - 设置一个国家唯一合法的政党，用于一党专政法律
set_owes_obligation_to - 设置一个国家是否对另一个国家负有义务
set_owner_of_provinces - 将一个州区的一组省份给予一个特定的国家
set_pop_literacy - 设置民众的识字率
set_pop_qualifications - 为给定类型设置民众的资格
set_pop_wealth - 设置民众的财富
set_relations - 设置两国之间的关系
set_ruling_interest_groups - 从一组利益集团中为范围内的国家创建一个政府
set_ruling_party - 将一个政党的所有利益集团加入政府，并从政府中移除所有其他利益集团
set_secret_goal - 为范围国家对另一个国家设置一个秘密AI目标
set_social_hierarchy - 设置范围国家的社会等级
set_state_name - 将范围内州的名称设置为一个本地化字符串
set_state_owner - 设置州的所有者
set_state_religion - 将国家的国教改为指定的宗教
set_state_type - 将一个州设置为某种类型(已整合、未整合、条约港)
set_strategy - 为范围国家设置AI策略
set_subsidized - 设置一个建筑是否被补贴
set_suppression - 开始/停止镇压范围内的政治运动
set_target_technology - 为一个日志条目设置一个(新的)目标技术范围
set_tariffs_export_priority - 为范围国家的一种商品设置出口优先关税
set_tariffs_import_priority - 为范围国家的一种商品设置进口优先关税
set_tariffs_no_priority - 为范围国家的一种商品设置没有进出口优先级的关税
set_tax_level - 设置范围国家的总体税收水平
set_tension - 设置两国之间的紧张关系
set_variable - 设置一个变量
set_war - 将一个外交博弈设置为战争
show_as_tooltip - 包含的效果只在工具提示中显示(但实际上不执行)
sort_global_variable_list - 对一个变量列表进行排序
sort_local_variable_list - 对一个变量列表进行排序
sort_variable_list - 对一个变量列表进行排序
start_building_construction - 在一个范围州开始建造一座建筑作为政府工程
start_enactment - 开始为范围国家颁布指定的法律类型
start_harvest_condition - 在范围州区开始一种类型的收获条件(如果已存在则刷新)
start_privately_funded_building_construction - 在一个范围州开始建造一座建筑作为私人工程
start_research_random_technology - 范围国家开始研究任何他们可以研究的随机技术
start_tutorial_lesson - 开始具有给定键的教程课程。如果教程未运行、课程已完成(或已在运行)，或课程无法触发(例如触发失败)，则无效
switch - 根据一个触发器来评估另一个触发器，并带有一个可选的回退触发器
take_on_scaled_debt - 转移相当于目标最大信贷额度上定义乘数的债务
teleport_to_front - 将范围编队传送到目标前线
transfer_character - 将当前范围人物转移到目标国家
transfer_to_formation - 将范围人物转移到目标编队
trigger_event - 为当前范围触发一个事件
try_form_government_with - 尝试与提供的利益集团组建新政府，如果无法达到提供的合法性，则会尝试尽可能多地将利益集团加入政府
unset_available_for_autonomous_investment - 将一种建筑类型设置为在当前范围州不可用于自主投资
unset_only_legal_party - 将国家恢复为非一党专政状态，允许多个政党存在(如果其他法律允许)
update_party_support - 更新范围国家的政党支持度
validate_subsidies - 验证一个国家建筑的补贴
violate_sovereignty_join - 目标加入范围战争
while - 当满足限制条件或达到设定的迭代次数时，重复包含的效果
